"""
Comprehensive tests for MarketHawk database models and relationships
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

# Import database setup
from app.core.database import init_db, close_db, get_db

# Import all models
from app.models.user import User, BrokerAccount, UserSession
from app.models.stock import Stock, MarketData, Watchlist, WatchlistItem, ScreeningResult
from app.models.trade import Trade, Position, TradeHistory
from app.models.portfolio import Portfolio, Strategy


async def create_test_user(db: AsyncSession) -> User:
    """Create a test user"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password_123",
        full_name="Test User",
        subscription_tier="free",
        is_active=True,
        is_verified=True
    )
    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user


async def create_test_stock(db: AsyncSession) -> Stock:
    """Create a test stock"""
    stock = Stock(
        symbol="AAPL",
        name="Apple Inc.",
        exchange="NASDAQ",
        sector="Technology",
        industry="Consumer Electronics",
        current_price=Decimal("150.00"),
        market_cap=2********0000,  # 2.5T
        volume=********,
        pe_ratio=Decimal("25.5"),
        dividend_yield=Decimal("0.5"),
        beta=Decimal("1.2"),
        is_active=True
    )
    db.add(stock)
    await db.commit()
    await db.refresh(stock)
    return stock


async def test_user_model():
    """Test User model creation and basic operations"""
    print("Testing User model...")
    
    await init_db()
    
    async with get_db() as db:
        # Create user
        user = await create_test_user(db)
        
        # Test user creation
        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.subscription_tier == "free"
        assert user.is_active is True
        assert user.created_at is not None
        
        # Test user query
        result = await db.execute(select(User).where(User.username == "testuser"))
        found_user = result.scalar_one_or_none()
        assert found_user is not None
        assert found_user.email == "<EMAIL>"
        
        print("✅ User model test passed")


async def test_stock_model():
    """Test Stock model creation and operations"""
    print("Testing Stock model...")
    
    async with get_db() as db:
        # Create stock
        stock = await create_test_stock(db)
        
        # Test stock creation
        assert stock.id is not None
        assert stock.symbol == "AAPL"
        assert stock.name == "Apple Inc."
        assert stock.current_price == Decimal("150.00")
        assert stock.market_cap == 2********0000
        
        # Test stock query
        result = await db.execute(select(Stock).where(Stock.symbol == "AAPL"))
        found_stock = result.scalar_one_or_none()
        assert found_stock is not None
        assert found_stock.name == "Apple Inc."
        
        print("✅ Stock model test passed")


async def test_portfolio_relationship():
    """Test User-Portfolio relationship"""
    print("Testing User-Portfolio relationship...")
    
    async with get_db() as db:
        # Create user
        user = await create_test_user(db)
        
        # Create portfolio
        portfolio = Portfolio(
            user_id=user.id,
            name="Test Portfolio",
            description="A test portfolio",
            initial_balance=Decimal("10000.00"),
            current_balance=Decimal("10500.00"),
            total_invested=Decimal("8000.00"),
            total_return=Decimal("500.00"),
            return_percentage=Decimal("5.0"),
            is_active=True
        )
        db.add(portfolio)
        await db.commit()
        await db.refresh(portfolio)
        
        # Test relationship
        assert portfolio.user_id == user.id
        
        # Test reverse relationship (if implemented)
        result = await db.execute(
            select(Portfolio).where(Portfolio.user_id == user.id)
        )
        user_portfolios = result.scalars().all()
        assert len(user_portfolios) == 1
        assert user_portfolios[0].name == "Test Portfolio"
        
        print("✅ User-Portfolio relationship test passed")


async def test_trade_relationships():
    """Test Trade model with all its relationships"""
    print("Testing Trade relationships...")
    
    async with get_db() as db:
        # Create dependencies
        user = await create_test_user(db)
        stock = await create_test_stock(db)
        
        # Create portfolio
        portfolio = Portfolio(
            user_id=user.id,
            name="Trading Portfolio",
            initial_balance=Decimal("10000.00"),
            current_balance=Decimal("9500.00"),
            is_active=True
        )
        db.add(portfolio)
        await db.commit()
        await db.refresh(portfolio)
        
        # Create broker account
        broker_account = BrokerAccount(
            user_id=user.id,
            broker_name="test_broker",
            account_id="TEST123",
            account_type="margin",
            is_active=True,
            is_paper_trading=True
        )
        db.add(broker_account)
        await db.commit()
        await db.refresh(broker_account)
        
        # Create trade
        trade = Trade(
            user_id=user.id,
            stock_id=stock.id,
            portfolio_id=portfolio.id,
            broker_account_id=broker_account.id,
            trade_type="buy",
            order_type="market",
            quantity=10,
            price=Decimal("150.00"),
            total_amount=Decimal("1500.00"),
            status="filled",
            broker_order_id="ORDER123"
        )
        db.add(trade)
        await db.commit()
        await db.refresh(trade)
        
        # Test all relationships
        assert trade.user_id == user.id
        assert trade.stock_id == stock.id
        assert trade.portfolio_id == portfolio.id
        assert trade.broker_account_id == broker_account.id
        
        # Test trade query with relationships
        result = await db.execute(
            select(Trade).where(Trade.broker_order_id == "ORDER123")
        )
        found_trade = result.scalar_one_or_none()
        assert found_trade is not None
        assert found_trade.quantity == 10
        assert found_trade.price == Decimal("150.00")
        
        print("✅ Trade relationships test passed")


async def test_watchlist_functionality():
    """Test Watchlist and WatchlistItem models"""
    print("Testing Watchlist functionality...")
    
    async with get_db() as db:
        # Create dependencies
        user = await create_test_user(db)
        stock = await create_test_stock(db)
        
        # Create watchlist
        watchlist = Watchlist(
            user_id=user.id,
            name="My Watchlist",
            description="Stocks I'm watching",
            is_default=True
        )
        db.add(watchlist)
        await db.commit()
        await db.refresh(watchlist)
        
        # Add stock to watchlist
        watchlist_item = WatchlistItem(
            watchlist_id=watchlist.id,
            stock_id=stock.id,
            notes="Potential buy candidate"
        )
        db.add(watchlist_item)
        await db.commit()
        await db.refresh(watchlist_item)
        
        # Test relationships
        assert watchlist.user_id == user.id
        assert watchlist_item.watchlist_id == watchlist.id
        assert watchlist_item.stock_id == stock.id
        
        # Test query watchlist items
        result = await db.execute(
            select(WatchlistItem).where(WatchlistItem.watchlist_id == watchlist.id)
        )
        items = result.scalars().all()
        assert len(items) == 1
        assert items[0].notes == "Potential buy candidate"
        
        print("✅ Watchlist functionality test passed")


async def run_all_tests():
    """Run all database model tests"""
    print("🚀 Starting MarketHawk Database Model Tests...\n")
    
    try:
        await test_user_model()
        await test_stock_model()
        await test_portfolio_relationship()
        await test_trade_relationships()
        await test_watchlist_functionality()
        
        print("\n🎉 All database model tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        raise
    finally:
        await close_db()


if __name__ == "__main__":
    asyncio.run(run_all_tests())
