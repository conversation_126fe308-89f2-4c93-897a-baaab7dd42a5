"""
MarketHawk - Advanced Stock Screener & Automated Trading Platform
Main FastAPI application entry point
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager
import uvicorn
import logging
from prometheus_client import make_asgi_app
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

from app.core.config import settings
from app.core.database import init_db, close_db
from app.api.v1 import auth, screening, trading, portfolio, backtesting
from app.services.data_provider import DataProvider
from app.services.screening_engine import ScreeningEngine
from app.services.trading_engine import TradingEngine

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize Sentry for error tracking
if settings.SENTRY_DSN:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        integrations=[
            FastApiIntegration(auto_enabling=True),
            SqlalchemyIntegration(),
        ],
        traces_sample_rate=0.1,
        environment=settings.ENVIRONMENT,
    )

# Global services
data_provider = None
screening_engine = None
trading_engine = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global data_provider, screening_engine, trading_engine
    
    logger.info("Starting MarketHawk application...")
    
    # Initialize database
    await init_db()
    
    # Initialize services
    data_provider = DataProvider()
    screening_engine = ScreeningEngine(data_provider)
    trading_engine = TradingEngine()
    
    # Start background services
    await data_provider.start()
    await screening_engine.start()
    
    logger.info("MarketHawk application started successfully")
    
    yield
    
    # Cleanup
    logger.info("Shutting down MarketHawk application...")
    
    if trading_engine:
        await trading_engine.stop()
    if screening_engine:
        await screening_engine.stop()
    if data_provider:
        await data_provider.stop()
    
    await close_db()
    
    logger.info("MarketHawk application shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="MarketHawk API",
    description="Advanced Stock Screener & Automated Trading Platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Security
security = HTTPBearer()

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.markethawk.io"]
)

# Prometheus metrics endpoint
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# API Routes
app.include_router(
    auth.router,
    prefix="/api/v1/auth",
    tags=["Authentication"]
)

app.include_router(
    screening.router,
    prefix="/api/v1/screening",
    tags=["Stock Screening"]
)

app.include_router(
    trading.router,
    prefix="/api/v1/trading",
    tags=["Trading"]
)

app.include_router(
    portfolio.router,
    prefix="/api/v1/portfolio",
    tags=["Portfolio Management"]
)

app.include_router(
    backtesting.router,
    prefix="/api/v1/backtesting",
    tags=["Backtesting"]
)

# Health check endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }

@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with service status"""
    global data_provider, screening_engine, trading_engine
    
    services_status = {
        "database": "unknown",
        "redis": "unknown",
        "data_provider": "unknown",
        "screening_engine": "unknown",
        "trading_engine": "unknown"
    }
    
    try:
        # Check database connection
        from app.core.database import get_db
        async with get_db() as db:
            services_status["database"] = "healthy"
    except Exception as e:
        services_status["database"] = f"unhealthy: {str(e)}"
    
    try:
        # Check Redis connection
        from app.core.database import get_redis
        redis = await get_redis()
        await redis.ping()
        services_status["redis"] = "healthy"
    except Exception as e:
        services_status["redis"] = f"unhealthy: {str(e)}"
    
    # Check service status
    if data_provider:
        services_status["data_provider"] = "healthy" if data_provider.is_running else "stopped"
    
    if screening_engine:
        services_status["screening_engine"] = "healthy" if screening_engine.is_running else "stopped"
    
    if trading_engine:
        services_status["trading_engine"] = "healthy" if trading_engine.is_running else "stopped"
    
    overall_status = "healthy" if all(
        status == "healthy" for status in services_status.values()
    ) else "degraded"
    
    return {
        "status": overall_status,
        "services": services_status,
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Welcome to MarketHawk API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "environment": settings.ENVIRONMENT
    }

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return HTTPException(
        status_code=500,
        detail="Internal server error"
    )

# Dependency to get services
async def get_data_provider() -> DataProvider:
    """Get data provider service"""
    global data_provider
    if not data_provider:
        raise HTTPException(status_code=503, detail="Data provider not available")
    return data_provider

async def get_screening_engine() -> ScreeningEngine:
    """Get screening engine service"""
    global screening_engine
    if not screening_engine:
        raise HTTPException(status_code=503, detail="Screening engine not available")
    return screening_engine

async def get_trading_engine() -> TradingEngine:
    """Get trading engine service"""
    global trading_engine
    if not trading_engine:
        raise HTTPException(status_code=503, detail="Trading engine not available")
    return trading_engine

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
