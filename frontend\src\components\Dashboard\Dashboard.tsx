import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  AccountBalance,
  ShowChart,
  Assessment,
  Speed,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { portfolioAPI, marketDataAPI, healthAPI } from '../../services/api';
import { useAppSelector } from '../../store/store';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning';
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  color = 'primary',
}) => {
  const getChangeColor = (change: number) => {
    if (change > 0) return 'success';
    if (change < 0) return 'error';
    return 'default';
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp fontSize="small" />;
    if (change < 0) return <TrendingDown fontSize="small" />;
    return null;
  };

  return (
    <Card className="metric-card">
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box color={`${color}.main`}>{icon}</Box>
          {change !== undefined && (
            <Chip
              icon={getChangeIcon(change)}
              label={`${change > 0 ? '+' : ''}${change.toFixed(2)}%`}
              color={getChangeColor(change)}
              size="small"
            />
          )}
        </Box>
        <Typography variant="h4" className="metric-value" color={`${color}.main`}>
          {value}
        </Typography>
        <Typography variant="body2" className="metric-label">
          {title}
        </Typography>
      </CardContent>
    </Card>
  );
};

const Dashboard: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const [marketStatus, setMarketStatus] = useState<any>(null);

  // Fetch portfolio data
  const { data: portfolios, isLoading: portfoliosLoading } = useQuery({
    queryKey: ['portfolios'],
    queryFn: portfolioAPI.getPortfolios,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch system health
  const { data: health } = useQuery({
    queryKey: ['health'],
    queryFn: healthAPI.getDetailedHealth,
    refetchInterval: 60000, // Refetch every minute
  });

  // Fetch market status
  useEffect(() => {
    const fetchMarketStatus = async () => {
      try {
        const status = await marketDataAPI.getMarketStatus();
        setMarketStatus(status);
      } catch (error) {
        console.error('Failed to fetch market status:', error);
      }
    };

    fetchMarketStatus();
    const interval = setInterval(fetchMarketStatus, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  // Calculate portfolio metrics
  const totalPortfolioValue = portfolios?.reduce(
    (sum, portfolio) => sum + (portfolio.current_capital || 0),
    0
  ) || 0;

  const totalReturn = portfolios?.reduce(
    (sum, portfolio) => sum + (portfolio.total_return || 0),
    0
  ) || 0;

  const totalReturnPercent = portfolios?.reduce(
    (sum, portfolio) => sum + (portfolio.total_return_percent || 0),
    0
  ) || 0;

  const activeStrategies = portfolios?.reduce(
    (sum, portfolio) => sum + (portfolio.strategies?.filter((s: any) => s.is_active).length || 0),
    0
  ) || 0;

  const totalTrades = portfolios?.reduce(
    (sum, portfolio) => sum + (portfolio.total_trades || 0),
    0
  ) || 0;

  const avgWinRate = portfolios?.length
    ? portfolios.reduce((sum, portfolio) => sum + (portfolio.win_rate || 0), 0) / portfolios.length
    : 0;

  if (portfoliosLoading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Loading Dashboard...
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={4}>
        <Typography variant="h3" gutterBottom>
          Welcome back, {user?.first_name || user?.username}! 👋
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h6" color="text.secondary">
            Market Status:
          </Typography>
          <Chip
            label={marketStatus?.is_open ? 'OPEN' : 'CLOSED'}
            color={marketStatus?.is_open ? 'success' : 'error'}
            variant="outlined"
          />
          {marketStatus?.timezone && (
            <Typography variant="body2" color="text.secondary">
              {marketStatus.timezone}
            </Typography>
          )}
        </Box>
      </Box>

      {/* System Health Alert */}
      {health && health.status !== 'healthy' && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          System Status: {health.status.toUpperCase()} - Some services may be degraded
        </Alert>
      )}

      {/* Key Metrics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Portfolio Value"
            value={`$${totalPortfolioValue.toLocaleString()}`}
            change={totalReturnPercent}
            icon={<AccountBalance fontSize="large" />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Return"
            value={`$${totalReturn.toLocaleString()}`}
            change={totalReturnPercent}
            icon={<ShowChart fontSize="large" />}
            color={totalReturn >= 0 ? 'success' : 'error'}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Active Strategies"
            value={activeStrategies}
            icon={<Speed fontSize="large" />}
            color="secondary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Win Rate"
            value={`${avgWinRate.toFixed(1)}%`}
            icon={<Assessment fontSize="large" />}
            color={avgWinRate >= 60 ? 'success' : avgWinRate >= 40 ? 'warning' : 'error'}
          />
        </Grid>
      </Grid>

      {/* Portfolio Overview */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Portfolio Overview
              </Typography>
              {portfolios && portfolios.length > 0 ? (
                <Box>
                  {portfolios.map((portfolio: any) => (
                    <Box
                      key={portfolio.id}
                      display="flex"
                      justifyContent="space-between"
                      alignItems="center"
                      py={2}
                      borderBottom="1px solid"
                      borderColor="divider"
                    >
                      <Box>
                        <Typography variant="h6">{portfolio.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {portfolio.strategies?.length || 0} strategies
                        </Typography>
                      </Box>
                      <Box textAlign="right">
                        <Typography variant="h6">
                          ${portfolio.current_capital?.toLocaleString()}
                        </Typography>
                        <Typography
                          variant="body2"
                          color={
                            portfolio.total_return_percent >= 0 ? 'success.main' : 'error.main'
                          }
                        >
                          {portfolio.total_return_percent >= 0 ? '+' : ''}
                          {portfolio.total_return_percent?.toFixed(2)}%
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box textAlign="center" py={4}>
                  <Typography variant="body1" color="text.secondary" mb={2}>
                    No portfolios found. Create your first portfolio to get started!
                  </Typography>
                  <Button variant="contained" color="primary">
                    Create Portfolio
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Quick Stats
              </Typography>
              <Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2">Total Trades:</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {totalTrades}
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2">Portfolios:</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {portfolios?.length || 0}
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2">Subscription:</Typography>
                  <Chip
                    label={user?.subscription_tier?.toUpperCase()}
                    size="small"
                    color={user?.subscription_tier === 'premium' ? 'primary' : 'default'}
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" py={1}>
                  <Typography variant="body2">Account Status:</Typography>
                  <Chip
                    label={user?.is_verified ? 'VERIFIED' : 'UNVERIFIED'}
                    size="small"
                    color={user?.is_verified ? 'success' : 'warning'}
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
