"""
Trading engine for automated order execution across multiple brokers
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy import select, and_

from app.core.config import settings
from app.core.database import get_db
from app.models.trade import Trade, OrderType, OrderSide, OrderStatus, Position
from app.models.user import BrokerAccount
from app.models.portfolio import Strategy
from app.models.stock import ScreeningResult
from app.services.broker_integration import BrokerManager

logger = logging.getLogger(__name__)

class TradingEngine:
    """Main trading engine for automated order execution"""
    
    def __init__(self):
        self.is_running = False
        self.broker_manager = BrokerManager()
        self.active_orders: Dict[int, Trade] = {}
        self.position_monitor_task = None
    
    async def start(self):
        """Start the trading engine"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Initialize broker connections
        await self.broker_manager.initialize()
        
        # Start background tasks
        self.position_monitor_task = asyncio.create_task(self._monitor_positions())
        asyncio.create_task(self._process_screening_signals())
        asyncio.create_task(self._monitor_orders())
        
        logger.info("Trading engine started")
    
    async def stop(self):
        """Stop the trading engine"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel background tasks
        if self.position_monitor_task:
            self.position_monitor_task.cancel()
        
        # Close broker connections
        await self.broker_manager.cleanup()
        
        logger.info("Trading engine stopped")
    
    async def _process_screening_signals(self):
        """Process screening signals and execute trades"""
        while self.is_running:
            try:
                # Get unprocessed screening results
                screening_results = await self._get_unprocessed_signals()
                
                for result in screening_results:
                    try:
                        await self._process_signal(result)
                    except Exception as e:
                        logger.error(f"Error processing signal {result.id}: {e}")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in signal processing loop: {e}")
                await asyncio.sleep(60)
    
    async def _get_unprocessed_signals(self) -> List[ScreeningResult]:
        """Get unprocessed screening signals"""
        try:
            async with get_db() as db:
                result = await db.execute(
                    select(ScreeningResult)
                    .where(
                        and_(
                            ScreeningResult.action_taken == "none",
                            ScreeningResult.score >= 70,  # Minimum score threshold
                            ScreeningResult.scan_timestamp >= datetime.utcnow() - timedelta(hours=1)
                        )
                    )
                    .order_by(ScreeningResult.score.desc())
                    .limit(50)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting unprocessed signals: {e}")
            return []
    
    async def _process_signal(self, signal: ScreeningResult):
        """Process a single screening signal"""
        try:
            # Get strategy and check if it's still active
            async with get_db() as db:
                strategy = await db.get(Strategy, signal.strategy_id)
                if not strategy or not strategy.is_active:
                    return
                
                # Check risk management rules
                if not await self._check_risk_management(strategy, signal):
                    await self._mark_signal_ignored(signal, "risk_management")
                    return
                
                # Check position limits
                if not await self._check_position_limits(strategy):
                    await self._mark_signal_ignored(signal, "position_limits")
                    return
                
                # Execute trade
                trade = await self._execute_trade(strategy, signal)
                if trade:
                    await self._mark_signal_traded(signal, trade.id)
                    logger.info(f"Trade executed for signal {signal.id}: {trade.id}")
                else:
                    await self._mark_signal_ignored(signal, "execution_failed")
                
        except Exception as e:
            logger.error(f"Error processing signal {signal.id}: {e}")
    
    async def _check_risk_management(self, strategy: Strategy, signal: ScreeningResult) -> bool:
        """Check risk management rules before executing trade"""
        try:
            # Check daily loss limit
            daily_pnl = await self._get_daily_pnl(strategy.portfolio_id)
            if daily_pnl < -strategy.portfolio.max_daily_loss * strategy.portfolio.current_capital:
                logger.warning(f"Daily loss limit exceeded for portfolio {strategy.portfolio_id}")
                return False
            
            # Check maximum position size
            position_value = signal.entry_price * self._calculate_position_size(strategy, signal)
            max_position_value = strategy.portfolio.current_capital * strategy.portfolio.max_position_size
            
            if position_value > max_position_value:
                logger.warning(f"Position size too large for signal {signal.id}")
                return False
            
            # Check portfolio risk
            total_risk = await self._calculate_portfolio_risk(strategy.portfolio_id)
            if total_risk > strategy.portfolio.max_total_risk:
                logger.warning(f"Portfolio risk limit exceeded: {total_risk}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking risk management: {e}")
            return False
    
    async def _check_position_limits(self, strategy: Strategy) -> bool:
        """Check if strategy can open new positions"""
        try:
            async with get_db() as db:
                # Count current positions for this strategy
                result = await db.execute(
                    select(Position)
                    .join(Trade)
                    .where(Trade.strategy_id == strategy.id)
                )
                current_positions = len(result.scalars().all())
                
                return current_positions < strategy.max_positions
                
        except Exception as e:
            logger.error(f"Error checking position limits: {e}")
            return False
    
    def _calculate_position_size(self, strategy: Strategy, signal: ScreeningResult) -> int:
        """Calculate position size based on strategy settings"""
        try:
            if strategy.position_size_method == "fixed":
                return int(strategy.position_size_value)
            
            elif strategy.position_size_method == "percent":
                available_capital = strategy.portfolio.available_cash
                position_value = available_capital * strategy.position_size_value
                return int(position_value / signal.entry_price)
            
            elif strategy.position_size_method == "kelly":
                # Simplified Kelly criterion
                # In production, this would use historical win rate and average win/loss
                win_rate = 0.6  # Default assumption
                avg_win = 0.02  # 2% average win
                avg_loss = 0.01  # 1% average loss
                
                kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
                kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
                
                position_value = strategy.portfolio.available_cash * kelly_fraction
                return int(position_value / signal.entry_price)
            
            else:
                # Default to 2% of portfolio
                position_value = strategy.portfolio.current_capital * 0.02
                return int(position_value / signal.entry_price)
                
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 100  # Default position size
    
    async def _execute_trade(self, strategy: Strategy, signal: ScreeningResult) -> Optional[Trade]:
        """Execute a trade based on screening signal"""
        try:
            # Get user's broker account
            async with get_db() as db:
                result = await db.execute(
                    select(BrokerAccount)
                    .where(
                        and_(
                            BrokerAccount.user_id == strategy.user_id,
                            BrokerAccount.is_active == True
                        )
                    )
                    .limit(1)
                )
                broker_account = result.scalar_one_or_none()
                
                if not broker_account:
                    logger.error(f"No active broker account for user {strategy.user_id}")
                    return None
                
                # Calculate position size
                quantity = self._calculate_position_size(strategy, signal)
                if quantity <= 0:
                    logger.warning(f"Invalid position size calculated: {quantity}")
                    return None
                
                # Create trade record
                trade = Trade(
                    user_id=strategy.user_id,
                    stock_id=signal.stock_id,
                    broker_account_id=broker_account.id,
                    strategy_id=strategy.id,
                    order_type=OrderType.MARKET if strategy.order_type == "market" else OrderType.LIMIT,
                    order_side=OrderSide.BUY,
                    quantity=quantity,
                    price=signal.entry_price if strategy.order_type == "limit" else None,
                    stop_loss_price=signal.stop_loss_price,
                    take_profit_price=signal.target_price,
                    is_paper_trade=broker_account.is_paper_trading,
                    status=OrderStatus.PENDING
                )
                
                db.add(trade)
                await db.commit()
                await db.refresh(trade)
                
                # Submit order to broker
                if not trade.is_paper_trade:
                    broker_order_id = await self.broker_manager.submit_order(
                        broker_account=broker_account,
                        symbol=signal.stock.symbol,
                        quantity=quantity,
                        order_type=trade.order_type.value,
                        order_side=trade.order_side.value,
                        price=trade.price
                    )
                    
                    if broker_order_id:
                        trade.broker_order_id = broker_order_id
                        trade.status = OrderStatus.SUBMITTED
                        trade.submitted_at = datetime.utcnow()
                        await db.commit()
                        
                        # Add to active orders for monitoring
                        self.active_orders[trade.id] = trade
                    else:
                        trade.status = OrderStatus.REJECTED
                        await db.commit()
                        return None
                else:
                    # Paper trading - simulate immediate fill
                    trade.status = OrderStatus.FILLED
                    trade.filled_quantity = quantity
                    trade.average_fill_price = signal.entry_price
                    trade.filled_at = datetime.utcnow()
                    trade.total_cost = quantity * signal.entry_price
                    await db.commit()
                
                return trade
                
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return None
    
    async def _monitor_orders(self):
        """Monitor active orders for fills and updates"""
        while self.is_running:
            try:
                for trade_id, trade in list(self.active_orders.items()):
                    try:
                        # Check order status with broker
                        order_status = await self.broker_manager.get_order_status(
                            trade.broker_account,
                            trade.broker_order_id
                        )
                        
                        if order_status:
                            await self._update_trade_status(trade, order_status)
                            
                            # Remove from active orders if completed
                            if order_status.get("status") in ["filled", "cancelled", "rejected"]:
                                del self.active_orders[trade_id]
                                
                    except Exception as e:
                        logger.error(f"Error monitoring order {trade_id}: {e}")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in order monitoring loop: {e}")
                await asyncio.sleep(30)
    
    async def _update_trade_status(self, trade: Trade, order_status: Dict[str, Any]):
        """Update trade status based on broker response"""
        try:
            async with get_db() as db:
                if order_status.get("status") == "filled":
                    trade.status = OrderStatus.FILLED
                    trade.filled_quantity = order_status.get("filled_quantity", trade.quantity)
                    trade.average_fill_price = order_status.get("average_price")
                    trade.filled_at = datetime.utcnow()
                    trade.commission = order_status.get("commission", 0)
                    trade.total_cost = (trade.filled_quantity * trade.average_fill_price) + trade.commission
                    
                elif order_status.get("status") == "cancelled":
                    trade.status = OrderStatus.CANCELLED
                    trade.cancelled_at = datetime.utcnow()
                    
                elif order_status.get("status") == "rejected":
                    trade.status = OrderStatus.REJECTED
                
                await db.commit()
                
        except Exception as e:
            logger.error(f"Error updating trade status: {e}")
    
    async def _monitor_positions(self):
        """Monitor open positions for exit signals"""
        while self.is_running:
            try:
                # Get all open positions
                positions = await self._get_open_positions()
                
                for position in positions:
                    try:
                        await self._check_exit_conditions(position)
                    except Exception as e:
                        logger.error(f"Error checking exit conditions for position {position.id}: {e}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in position monitoring loop: {e}")
                await asyncio.sleep(120)
    
    async def _get_open_positions(self) -> List[Position]:
        """Get all open positions"""
        try:
            async with get_db() as db:
                result = await db.execute(select(Position))
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting open positions: {e}")
            return []
    
    async def _check_exit_conditions(self, position: Position):
        """Check if position should be closed based on exit rules"""
        # This would implement exit logic based on strategy rules
        # For now, just a placeholder
        pass
    
    async def _mark_signal_traded(self, signal: ScreeningResult, trade_id: int):
        """Mark screening signal as traded"""
        try:
            async with get_db() as db:
                signal.action_taken = "trade_executed"
                signal.trade_id = trade_id
                await db.commit()
        except Exception as e:
            logger.error(f"Error marking signal as traded: {e}")
    
    async def _mark_signal_ignored(self, signal: ScreeningResult, reason: str):
        """Mark screening signal as ignored"""
        try:
            async with get_db() as db:
                signal.action_taken = "ignored"
                signal.metadata = {"ignore_reason": reason}
                await db.commit()
        except Exception as e:
            logger.error(f"Error marking signal as ignored: {e}")
    
    async def _get_daily_pnl(self, portfolio_id: int) -> float:
        """Get daily P&L for portfolio"""
        # Placeholder implementation
        return 0.0
    
    async def _calculate_portfolio_risk(self, portfolio_id: int) -> float:
        """Calculate total portfolio risk"""
        # Placeholder implementation
        return 0.1

    async def submit_order(self, trade: Trade) -> bool:
        """
        Submit order for execution (API endpoint method)

        Args:
            trade: Trade object to execute

        Returns:
            True if order submitted successfully
        """
        try:
            # Add to active orders for monitoring
            self.active_orders[trade.id] = trade

            # For paper trading, simulate immediate execution
            if trade.is_paper_trade:
                await self._simulate_paper_trade_execution(trade)
                return True

            # For live trading, submit to broker
            broker_account = trade.broker_account
            if not broker_account:
                logger.error(f"No broker account found for trade {trade.id}")
                return False

            # Submit to broker via broker manager
            success = await self.broker_manager.submit_order(
                broker_account.broker_name,
                trade
            )

            if success:
                logger.info(f"Order submitted successfully: {trade.id}")
            else:
                logger.error(f"Failed to submit order: {trade.id}")
                # Remove from active orders if submission failed
                self.active_orders.pop(trade.id, None)

            return success

        except Exception as e:
            logger.error(f"Error submitting order {trade.id}: {e}")
            return False

    async def cancel_order(self, trade: Trade) -> bool:
        """
        Cancel pending order (API endpoint method)

        Args:
            trade: Trade to cancel

        Returns:
            True if order cancelled successfully
        """
        try:
            # Remove from active orders
            self.active_orders.pop(trade.id, None)

            # For paper trading, just update status
            if trade.is_paper_trade:
                async with get_db() as db:
                    trade.status = OrderStatus.CANCELLED
                    trade.cancelled_at = datetime.utcnow()
                    await db.commit()
                return True

            # For live trading, cancel with broker
            broker_account = trade.broker_account
            if not broker_account:
                logger.error(f"No broker account found for trade {trade.id}")
                return False

            success = await self.broker_manager.cancel_order(
                broker_account.broker_name,
                trade.broker_order_id
            )

            if success:
                async with get_db() as db:
                    trade.status = OrderStatus.CANCELLED
                    trade.cancelled_at = datetime.utcnow()
                    await db.commit()
                logger.info(f"Order cancelled successfully: {trade.id}")
            else:
                logger.error(f"Failed to cancel order: {trade.id}")

            return success

        except Exception as e:
            logger.error(f"Error cancelling order {trade.id}: {e}")
            return False

    async def _simulate_paper_trade_execution(self, trade: Trade):
        """Simulate paper trade execution"""
        try:
            async with get_db() as db:
                # Simulate immediate execution at current market price
                # In a real implementation, this would get actual market data

                # Set execution details
                trade.status = OrderStatus.FILLED
                trade.filled_quantity = trade.quantity
                trade.average_fill_price = trade.price or 100.0  # Default price for simulation
                trade.commission = 0.0  # No commission for paper trading
                trade.filled_at = datetime.utcnow()

                # Calculate total cost
                if trade.order_side == OrderSide.BUY:
                    trade.total_cost = trade.quantity * trade.average_fill_price
                else:
                    trade.total_cost = trade.quantity * trade.average_fill_price

                await db.commit()

                # Update position
                await self._update_position_for_trade(trade)

                logger.info(f"Paper trade executed: {trade.id}")

        except Exception as e:
            logger.error(f"Error simulating paper trade execution: {e}")

    async def _update_position_for_trade(self, trade: Trade):
        """Update position after trade execution"""
        try:
            async with get_db() as db:
                # Find existing position
                result = await db.execute(
                    select(Position).where(
                        and_(
                            Position.user_id == trade.user_id,
                            Position.stock_id == trade.stock_id,
                            Position.broker_account_id == trade.broker_account_id
                        )
                    )
                )
                position = result.scalar_one_or_none()

                if not position:
                    # Create new position
                    position = Position(
                        user_id=trade.user_id,
                        stock_id=trade.stock_id,
                        broker_account_id=trade.broker_account_id,
                        portfolio_id=trade.portfolio_id,
                        quantity=0,
                        average_cost=0.0,
                        total_cost=0.0,
                        realized_pnl=0.0
                    )
                    db.add(position)

                # Update position based on trade
                if trade.order_side == OrderSide.BUY:
                    # Add to position
                    total_cost = position.total_cost + (trade.quantity * trade.average_fill_price)
                    total_quantity = position.quantity + trade.quantity

                    if total_quantity > 0:
                        position.average_cost = total_cost / total_quantity

                    position.quantity = total_quantity
                    position.total_cost = total_cost

                else:  # SELL
                    # Reduce position
                    if position.quantity >= trade.quantity:
                        # Calculate realized P&L
                        cost_basis = position.average_cost * trade.quantity
                        proceeds = trade.quantity * trade.average_fill_price
                        realized_pnl = proceeds - cost_basis

                        position.quantity -= trade.quantity
                        position.total_cost -= cost_basis
                        position.realized_pnl += realized_pnl

                        # Update trade with realized P&L
                        trade.realized_pnl = realized_pnl

                position.updated_at = datetime.utcnow()
                await db.commit()

        except Exception as e:
            logger.error(f"Error updating position for trade {trade.id}: {e}")
