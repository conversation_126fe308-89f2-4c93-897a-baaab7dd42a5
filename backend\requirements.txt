# Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.0
influxdb-client>=1.38.0

# Cache and Message Queue
redis>=5.0.0
celery>=5.3.0
kombu>=5.3.0

# Authentication and Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=3.4.0

# HTTP Requests
httpx>=0.25.0
aiohttp>=3.9.0
requests>=2.31.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0
yfinance>=0.2.0

# API Clients
alpha-vantage>=2.3.0
polygon-api-client>=1.13.0

# WebSocket
websockets>=10.3,<12.0
python-socketio>=5.10.0

# Configuration
pydantic>=2.5.0
pydantic-settings>=2.1.0
python-dotenv>=1.0.0

# Logging and Monitoring
structlog>=23.2.0
prometheus-client>=0.19.0
sentry-sdk[fastapi]>=1.38.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# Development
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0

# Async Support
aiofiles>=23.2.0
asyncpg>=0.29.0
anyio>=3.7.0

# Date and Time
python-dateutil>=2.8.0
pytz>=2023.3

# Broker APIs
alpaca-trade-api>=3.0.0

# Machine Learning (Optional)
scikit-learn>=1.3.0
joblib>=1.3.0

# Utilities
click>=8.1.0
rich>=13.7.0
typer>=0.9.0
