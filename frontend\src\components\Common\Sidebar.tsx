import React from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Divider,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  Dashboard,
  Search,
  TrendingUp,
  AccountBalance,
  Analytics,
  Settings,
  Help,
  Notifications,
  Security,
  Assessment,
  AutoGraph,
  MonetizationOn,
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';

interface SidebarProps {
  open: boolean;
  onToggle: () => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  badge?: string;
  requiredSubscription?: 'free' | 'premium' | 'enterprise';
  disabled?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ open, onToggle }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Dashboard />,
      path: '/dashboard',
    },
    {
      id: 'screener',
      label: 'Stock Screener',
      icon: <Search />,
      path: '/screener',
    },
    {
      id: 'trading',
      label: 'Trading',
      icon: <TrendingUp />,
      path: '/trading',
      badge: 'BETA',
    },
    {
      id: 'portfolio',
      label: 'Portfolio',
      icon: <AccountBalance />,
      path: '/portfolio',
    },
    {
      id: 'backtesting',
      label: 'Backtesting',
      icon: <Analytics />,
      path: '/backtesting',
      requiredSubscription: 'premium',
    },
    {
      id: 'analytics',
      label: 'Advanced Analytics',
      icon: <Assessment />,
      path: '/analytics',
      requiredSubscription: 'premium',
    },
    {
      id: 'algorithms',
      label: 'Trading Algorithms',
      icon: <AutoGraph />,
      path: '/algorithms',
      requiredSubscription: 'enterprise',
    },
    {
      id: 'risk-management',
      label: 'Risk Management',
      icon: <Security />,
      path: '/risk-management',
      requiredSubscription: 'premium',
    },
  ];

  const settingsItems: MenuItem[] = [
    {
      id: 'subscription',
      label: 'Subscription',
      icon: <MonetizationOn />,
      path: '/subscription',
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: <Notifications />,
      path: '/notifications',
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings />,
      path: '/settings',
    },
    {
      id: 'help',
      label: 'Help & Support',
      icon: <Help />,
      path: '/help',
    },
  ];

  const handleNavigation = (path: string, requiredSubscription?: string) => {
    if (requiredSubscription && user) {
      const subscriptionHierarchy = {
        free: 0,
        premium: 1,
        enterprise: 2,
      };

      const userTierLevel = subscriptionHierarchy[user.subscription_tier as keyof typeof subscriptionHierarchy] || 0;
      const requiredTierLevel = subscriptionHierarchy[requiredSubscription as keyof typeof subscriptionHierarchy];

      if (userTierLevel < requiredTierLevel) {
        // Navigate to subscription page instead
        navigate('/subscription');
        return;
      }
    }
    
    navigate(path);
  };

  const isItemDisabled = (item: MenuItem) => {
    if (!item.requiredSubscription || !user) return false;
    
    const subscriptionHierarchy = {
      free: 0,
      premium: 1,
      enterprise: 2,
    };

    const userTierLevel = subscriptionHierarchy[user.subscription_tier as keyof typeof subscriptionHierarchy] || 0;
    const requiredTierLevel = subscriptionHierarchy[item.requiredSubscription as keyof typeof subscriptionHierarchy];

    return userTierLevel < requiredTierLevel;
  };

  const renderMenuItem = (item: MenuItem) => {
    const isActive = location.pathname === item.path;
    const disabled = isItemDisabled(item);

    const listItem = (
      <ListItem key={item.id} disablePadding sx={{ display: 'block' }}>
        <ListItemButton
          onClick={() => handleNavigation(item.path, item.requiredSubscription)}
          disabled={disabled}
          sx={{
            minHeight: 48,
            justifyContent: open ? 'initial' : 'center',
            px: 2.5,
            backgroundColor: isActive ? 'primary.main' : 'transparent',
            color: isActive ? 'primary.contrastText' : 'text.primary',
            '&:hover': {
              backgroundColor: isActive ? 'primary.dark' : 'action.hover',
            },
            '&.Mui-disabled': {
              opacity: 0.5,
            },
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 0,
              mr: open ? 3 : 'auto',
              justifyContent: 'center',
              color: isActive ? 'primary.contrastText' : 'text.primary',
            }}
          >
            {item.icon}
          </ListItemIcon>
          <ListItemText
            primary={item.label}
            sx={{
              opacity: open ? 1 : 0,
              '& .MuiListItemText-primary': {
                fontSize: '0.875rem',
                fontWeight: isActive ? 600 : 400,
              },
            }}
          />
          {open && item.badge && (
            <Chip
              label={item.badge}
              size="small"
              color="secondary"
              sx={{ ml: 1, height: 20, fontSize: '0.625rem' }}
            />
          )}
          {open && disabled && (
            <Chip
              label="PRO"
              size="small"
              color="warning"
              sx={{ ml: 1, height: 20, fontSize: '0.625rem' }}
            />
          )}
        </ListItemButton>
      </ListItem>
    );

    if (!open && disabled) {
      return (
        <Tooltip
          key={item.id}
          title={`${item.label} - Requires ${item.requiredSubscription} subscription`}
          placement="right"
        >
          <div>{listItem}</div>
        </Tooltip>
      );
    }

    return listItem;
  };

  const drawerWidth = 240;

  return (
    <Drawer
      variant="permanent"
      open={open}
      sx={{
        width: open ? drawerWidth : 60,
        flexShrink: 0,
        whiteSpace: 'nowrap',
        boxSizing: 'border-box',
        '& .MuiDrawer-paper': {
          width: open ? drawerWidth : 60,
          transition: 'width 0.3s ease',
          overflowX: 'hidden',
          backgroundColor: 'background.paper',
          borderRight: '1px solid rgba(255, 255, 255, 0.1)',
          mt: 8, // Account for navbar height
        },
      }}
    >
      <Box sx={{ overflow: 'auto', height: '100%' }}>
        {/* Main Navigation */}
        <List>
          {menuItems.map(renderMenuItem)}
        </List>

        <Divider sx={{ my: 1 }} />

        {/* Settings Navigation */}
        <List>
          {open && (
            <ListItem>
              <Typography
                variant="overline"
                sx={{
                  fontSize: '0.75rem',
                  fontWeight: 600,
                  color: 'text.secondary',
                  px: 2,
                }}
              >
                Settings
              </Typography>
            </ListItem>
          )}
          {settingsItems.map(renderMenuItem)}
        </List>

        {/* Subscription Status */}
        {open && user && (
          <Box sx={{ p: 2, mt: 'auto' }}>
            <Box
              sx={{
                p: 2,
                borderRadius: 2,
                backgroundColor: 'background.default',
                border: '1px solid rgba(255, 255, 255, 0.1)',
              }}
            >
              <Typography variant="body2" fontWeight={600} gutterBottom>
                Current Plan
              </Typography>
              <Chip
                label={user.subscription_tier.toUpperCase()}
                size="small"
                color={
                  user.subscription_tier === 'enterprise'
                    ? 'secondary'
                    : user.subscription_tier === 'premium'
                    ? 'primary'
                    : 'default'
                }
                sx={{ mb: 1 }}
              />
              {user.subscription_tier === 'free' && (
                <Typography variant="caption" color="text.secondary" display="block">
                  Upgrade for advanced features
                </Typography>
              )}
            </Box>
          </Box>
        )}
      </Box>
    </Drawer>
  );
};

export default Sidebar;
