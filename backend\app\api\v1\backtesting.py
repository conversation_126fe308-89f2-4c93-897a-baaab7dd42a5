"""
Backtesting API endpoints
Provides comprehensive backtesting functionality for trading strategies
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query, BackgroundTasks
from fastapi.security import HTTPBearer
from pydantic import BaseModel, Field, validator
from sqlalchemy import select, and_, or_, desc
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import uuid

from app.core.database import get_db, CacheManager
from app.core.security import get_current_active_user, require_subscription_tier
from app.models.user import User
from app.models.stock import Stock
from app.models.portfolio import Strategy
from app.services.backtesting_engine import BacktestingEngine
from app.services.data_provider import DataProvider
from app.utils.rate_limiter import RateLimiter
from app.core.config import settings

router = APIRouter()
security = HTTPBearer()

# Rate limiter for backtesting operations
backtesting_rate_limiter = RateLimiter(
    max_requests=10,  # Free tier: 10 backtests per hour
    time_window=3600,
    premium_max_requests=100,  # Premium tier: 100 backtests per hour
    enterprise_max_requests=1000  # Enterprise tier: 1000 backtests per hour
)

# Enums
class BacktestStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

class BacktestPeriod(str, Enum):
    MONTH_1 = "1m"
    MONTH_3 = "3m"
    MONTH_6 = "6m"
    YEAR_1 = "1y"
    YEAR_2 = "2y"
    YEAR_5 = "5y"
    CUSTOM = "custom"

# Pydantic models
class BacktestRequest(BaseModel):
    """Backtest request model"""
    strategy_id: int
    start_date: datetime
    end_date: datetime
    initial_capital: float = Field(default=100000, gt=0, le=10000000)
    commission_per_trade: float = Field(default=1.0, ge=0, le=100)
    slippage_percent: float = Field(default=0.1, ge=0, le=5.0)
    symbols: Optional[List[str]] = Field(None, max_items=500)
    benchmark_symbol: str = Field(default="SPY")
    max_positions: int = Field(default=10, ge=1, le=100)
    position_sizing: str = Field(default="equal_weight", pattern="^(equal_weight|risk_parity|kelly)$")
    rebalance_frequency: str = Field(default="monthly", pattern="^(daily|weekly|monthly|quarterly)$")

    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('End date must be after start date')
        return v

class BacktestResponse(BaseModel):
    """Backtest response model"""
    id: str
    strategy_id: int
    status: BacktestStatus
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    total_return: float
    total_return_percent: float
    annualized_return: float
    max_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    volatility: float
    beta: float
    alpha: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    benchmark_return: float
    benchmark_volatility: float
    created_at: str
    completed_at: Optional[str]
    error_message: Optional[str]

class BacktestTrade(BaseModel):
    """Individual backtest trade"""
    symbol: str
    entry_date: str
    exit_date: Optional[str]
    entry_price: float
    exit_price: Optional[float]
    quantity: int
    side: str  # "buy" or "sell"
    pnl: Optional[float]
    pnl_percent: Optional[float]
    commission: float
    duration_days: Optional[int]
    entry_reason: str
    exit_reason: Optional[str]

class BacktestEquityCurve(BaseModel):
    """Equity curve data point"""
    date: str
    portfolio_value: float
    benchmark_value: float
    drawdown: float
    daily_return: float
    cumulative_return: float

class BacktestDetailResponse(BaseModel):
    """Detailed backtest results"""
    backtest: BacktestResponse
    trades: List[BacktestTrade]
    equity_curve: List[BacktestEquityCurve]
    monthly_returns: Dict[str, float]
    position_history: List[Dict[str, Any]]
    risk_metrics: Dict[str, float]

# Global backtest jobs tracker
backtest_jobs: Dict[str, Dict[str, Any]] = {}

@router.post("/run", response_model=Dict[str, str])
async def run_backtest(
    backtest_request: BacktestRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Run a backtest for a strategy"""
    # Apply rate limiting
    await backtesting_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier
    )
    
    async with get_db() as db:
        # Verify strategy ownership
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == backtest_request.strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = result.scalar_one_or_none()
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )
        
        # Generate backtest ID
        backtest_id = str(uuid.uuid4())
        
        # Initialize backtest job
        backtest_jobs[backtest_id] = {
            "id": backtest_id,
            "strategy_id": strategy.id,
            "user_id": current_user.id,
            "status": BacktestStatus.PENDING,
            "request": backtest_request.dict(),
            "created_at": datetime.utcnow(),
            "completed_at": None,
            "error_message": None,
            "results": None
        }
        
        # Start background backtesting task
        background_tasks.add_task(
            _run_backtest_background,
            backtest_id,
            strategy,
            backtest_request,
            current_user
        )
        
        return {"backtest_id": backtest_id, "status": "started"}

async def _run_backtest_background(
    backtest_id: str,
    strategy: Strategy,
    request: BacktestRequest,
    user: User
):
    """Background task to run backtest"""
    try:
        job_data = backtest_jobs[backtest_id]
        job_data["status"] = BacktestStatus.RUNNING
        
        # Initialize backtesting engine
        backtesting_engine = BacktestingEngine()
        
        # Run backtest
        results = await backtesting_engine.run_backtest(
            strategy=strategy,
            start_date=request.start_date,
            end_date=request.end_date,
            initial_capital=request.initial_capital,
            commission_per_trade=request.commission_per_trade,
            slippage_percent=request.slippage_percent,
            symbols=request.symbols,
            benchmark_symbol=request.benchmark_symbol,
            max_positions=request.max_positions,
            position_sizing=request.position_sizing,
            rebalance_frequency=request.rebalance_frequency
        )
        
        # Store results
        job_data["results"] = results
        job_data["status"] = BacktestStatus.COMPLETED
        job_data["completed_at"] = datetime.utcnow()
        
    except Exception as e:
        # Mark job as failed
        job_data["status"] = BacktestStatus.FAILED
        job_data["error_message"] = str(e)
        job_data["completed_at"] = datetime.utcnow()

@router.get("/jobs/{backtest_id}", response_model=BacktestResponse)
async def get_backtest_status(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get backtest job status"""
    if backtest_id not in backtest_jobs:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Backtest job not found"
        )
    
    job_data = backtest_jobs[backtest_id]
    
    # Check if user owns this backtest
    if job_data["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this backtest"
        )
    
    results = job_data.get("results", {})
    
    return BacktestResponse(
        id=job_data["id"],
        strategy_id=job_data["strategy_id"],
        status=job_data["status"],
        start_date=job_data["request"]["start_date"],
        end_date=job_data["request"]["end_date"],
        initial_capital=job_data["request"]["initial_capital"],
        final_capital=results.get("final_capital", 0),
        total_return=results.get("total_return", 0),
        total_return_percent=results.get("total_return_percent", 0),
        annualized_return=results.get("annualized_return", 0),
        max_drawdown=results.get("max_drawdown", 0),
        sharpe_ratio=results.get("sharpe_ratio", 0),
        sortino_ratio=results.get("sortino_ratio", 0),
        calmar_ratio=results.get("calmar_ratio", 0),
        volatility=results.get("volatility", 0),
        beta=results.get("beta", 0),
        alpha=results.get("alpha", 0),
        win_rate=results.get("win_rate", 0),
        profit_factor=results.get("profit_factor", 0),
        total_trades=results.get("total_trades", 0),
        winning_trades=results.get("winning_trades", 0),
        losing_trades=results.get("losing_trades", 0),
        avg_win=results.get("avg_win", 0),
        avg_loss=results.get("avg_loss", 0),
        max_consecutive_wins=results.get("max_consecutive_wins", 0),
        max_consecutive_losses=results.get("max_consecutive_losses", 0),
        benchmark_return=results.get("benchmark_return", 0),
        benchmark_volatility=results.get("benchmark_volatility", 0),
        created_at=job_data["created_at"].isoformat(),
        completed_at=job_data["completed_at"].isoformat() if job_data["completed_at"] else None,
        error_message=job_data["error_message"]
    )

@router.get("/jobs/{backtest_id}/details", response_model=BacktestDetailResponse)
async def get_backtest_details(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get detailed backtest results"""
    if current_user.subscription_tier == "free":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Detailed backtest results require premium subscription"
        )
    
    if backtest_id not in backtest_jobs:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Backtest job not found"
        )
    
    job_data = backtest_jobs[backtest_id]
    
    # Check if user owns this backtest
    if job_data["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this backtest"
        )
    
    if job_data["status"] != BacktestStatus.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Backtest not completed yet"
        )
    
    results = job_data["results"]
    
    # Get basic backtest info
    backtest_info = await get_backtest_status(backtest_id, current_user)
    
    # Convert detailed results
    trades = [
        BacktestTrade(
            symbol=trade["symbol"],
            entry_date=trade["entry_date"],
            exit_date=trade.get("exit_date"),
            entry_price=trade["entry_price"],
            exit_price=trade.get("exit_price"),
            quantity=trade["quantity"],
            side=trade["side"],
            pnl=trade.get("pnl"),
            pnl_percent=trade.get("pnl_percent"),
            commission=trade["commission"],
            duration_days=trade.get("duration_days"),
            entry_reason=trade["entry_reason"],
            exit_reason=trade.get("exit_reason")
        )
        for trade in results.get("trades", [])
    ]
    
    equity_curve = [
        BacktestEquityCurve(
            date=point["date"],
            portfolio_value=point["portfolio_value"],
            benchmark_value=point["benchmark_value"],
            drawdown=point["drawdown"],
            daily_return=point["daily_return"],
            cumulative_return=point["cumulative_return"]
        )
        for point in results.get("equity_curve", [])
    ]
    
    return BacktestDetailResponse(
        backtest=backtest_info,
        trades=trades,
        equity_curve=equity_curve,
        monthly_returns=results.get("monthly_returns", {}),
        position_history=results.get("position_history", []),
        risk_metrics=results.get("risk_metrics", {})
    )

@router.get("/history", response_model=List[BacktestResponse])
async def get_backtest_history(
    current_user: User = Depends(get_current_active_user),
    strategy_id: Optional[int] = Query(None),
    limit: int = Query(50, ge=1, le=200)
):
    """Get user's backtest history"""
    user_backtests = []

    for job_id, job_data in backtest_jobs.items():
        if job_data["user_id"] == current_user.id:
            if strategy_id is None or job_data["strategy_id"] == strategy_id:
                results = job_data.get("results", {})

                user_backtests.append(BacktestResponse(
                    id=job_data["id"],
                    strategy_id=job_data["strategy_id"],
                    status=job_data["status"],
                    start_date=job_data["request"]["start_date"],
                    end_date=job_data["request"]["end_date"],
                    initial_capital=job_data["request"]["initial_capital"],
                    final_capital=results.get("final_capital", 0),
                    total_return=results.get("total_return", 0),
                    total_return_percent=results.get("total_return_percent", 0),
                    annualized_return=results.get("annualized_return", 0),
                    max_drawdown=results.get("max_drawdown", 0),
                    sharpe_ratio=results.get("sharpe_ratio", 0),
                    sortino_ratio=results.get("sortino_ratio", 0),
                    calmar_ratio=results.get("calmar_ratio", 0),
                    volatility=results.get("volatility", 0),
                    beta=results.get("beta", 0),
                    alpha=results.get("alpha", 0),
                    win_rate=results.get("win_rate", 0),
                    profit_factor=results.get("profit_factor", 0),
                    total_trades=results.get("total_trades", 0),
                    winning_trades=results.get("winning_trades", 0),
                    losing_trades=results.get("losing_trades", 0),
                    avg_win=results.get("avg_win", 0),
                    avg_loss=results.get("avg_loss", 0),
                    max_consecutive_wins=results.get("max_consecutive_wins", 0),
                    max_consecutive_losses=results.get("max_consecutive_losses", 0),
                    benchmark_return=results.get("benchmark_return", 0),
                    benchmark_volatility=results.get("benchmark_volatility", 0),
                    created_at=job_data["created_at"].isoformat(),
                    completed_at=job_data["completed_at"].isoformat() if job_data["completed_at"] else None,
                    error_message=job_data["error_message"]
                ))

    # Sort by creation date (newest first) and limit
    user_backtests.sort(key=lambda x: x.created_at, reverse=True)
    return user_backtests[:limit]

@router.delete("/jobs/{backtest_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_backtest(
    backtest_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a backtest job"""
    if backtest_id not in backtest_jobs:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Backtest job not found"
        )

    job_data = backtest_jobs[backtest_id]

    # Check if user owns this backtest
    if job_data["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this backtest"
        )

    # Remove from memory
    del backtest_jobs[backtest_id]

@router.get("/presets")
async def get_backtest_presets(
    current_user: User = Depends(get_current_active_user)
):
    """Get predefined backtest configurations"""
    presets = {
        "quick_test": {
            "name": "Quick Test",
            "description": "3-month backtest with default settings",
            "period": BacktestPeriod.MONTH_3,
            "initial_capital": 100000,
            "commission_per_trade": 1.0,
            "slippage_percent": 0.1,
            "max_positions": 10,
            "position_sizing": "equal_weight",
            "rebalance_frequency": "monthly"
        },
        "comprehensive": {
            "name": "Comprehensive Analysis",
            "description": "2-year backtest with detailed analysis",
            "period": BacktestPeriod.YEAR_2,
            "initial_capital": 100000,
            "commission_per_trade": 1.0,
            "slippage_percent": 0.1,
            "max_positions": 20,
            "position_sizing": "risk_parity",
            "rebalance_frequency": "monthly"
        },
        "long_term": {
            "name": "Long-term Strategy",
            "description": "5-year backtest for long-term strategies",
            "period": BacktestPeriod.YEAR_5,
            "initial_capital": 100000,
            "commission_per_trade": 1.0,
            "slippage_percent": 0.1,
            "max_positions": 15,
            "position_sizing": "equal_weight",
            "rebalance_frequency": "quarterly"
        }
    }

    # Filter based on subscription tier
    if current_user.subscription_tier == "free":
        # Free tier only gets quick test
        return {"quick_test": presets["quick_test"]}
    elif current_user.subscription_tier == "premium":
        # Premium gets quick test and comprehensive
        return {
            "quick_test": presets["quick_test"],
            "comprehensive": presets["comprehensive"]
        }
    else:
        # Enterprise gets all presets
        return presets

@router.get("/benchmarks")
async def get_available_benchmarks(
    current_user: User = Depends(get_current_active_user)
):
    """Get available benchmark symbols"""
    benchmarks = {
        "SPY": {
            "name": "SPDR S&P 500 ETF",
            "description": "S&P 500 Index",
            "category": "Large Cap US Equity"
        },
        "QQQ": {
            "name": "Invesco QQQ ETF",
            "description": "NASDAQ 100 Index",
            "category": "Technology Heavy US Equity"
        },
        "IWM": {
            "name": "iShares Russell 2000 ETF",
            "description": "Russell 2000 Small Cap Index",
            "category": "Small Cap US Equity"
        },
        "VTI": {
            "name": "Vanguard Total Stock Market ETF",
            "description": "Total US Stock Market",
            "category": "Total Market US Equity"
        },
        "AGG": {
            "name": "iShares Core US Aggregate Bond ETF",
            "description": "US Aggregate Bond Index",
            "category": "US Bonds"
        }
    }

    # Premium and enterprise users get additional international benchmarks
    if current_user.subscription_tier in ["premium", "enterprise"]:
        benchmarks.update({
            "VEA": {
                "name": "Vanguard FTSE Developed Markets ETF",
                "description": "Developed International Markets",
                "category": "International Developed Equity"
            },
            "VWO": {
                "name": "Vanguard FTSE Emerging Markets ETF",
                "description": "Emerging Markets",
                "category": "Emerging Markets Equity"
            },
            "GLD": {
                "name": "SPDR Gold Shares",
                "description": "Gold Commodity",
                "category": "Commodities"
            }
        })

    return benchmarks

@router.post("/compare")
async def compare_backtests(
    backtest_ids: List[str],
    current_user: User = Depends(get_current_active_user)
):
    """Compare multiple backtest results"""
    if current_user.subscription_tier == "free":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Backtest comparison requires premium subscription"
        )

    if len(backtest_ids) > 5:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot compare more than 5 backtests at once"
        )

    comparison_data = []

    for backtest_id in backtest_ids:
        if backtest_id not in backtest_jobs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Backtest {backtest_id} not found"
            )

        job_data = backtest_jobs[backtest_id]

        # Check ownership
        if job_data["user_id"] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied to backtest {backtest_id}"
            )

        if job_data["status"] != BacktestStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Backtest {backtest_id} not completed"
            )

        results = job_data["results"]
        comparison_data.append({
            "backtest_id": backtest_id,
            "strategy_id": job_data["strategy_id"],
            "total_return_percent": results.get("total_return_percent", 0),
            "annualized_return": results.get("annualized_return", 0),
            "max_drawdown": results.get("max_drawdown", 0),
            "sharpe_ratio": results.get("sharpe_ratio", 0),
            "volatility": results.get("volatility", 0),
            "win_rate": results.get("win_rate", 0),
            "profit_factor": results.get("profit_factor", 0),
            "total_trades": results.get("total_trades", 0)
        })

    return {
        "comparison": comparison_data,
        "summary": {
            "best_return": max(comparison_data, key=lambda x: x["total_return_percent"]),
            "best_sharpe": max(comparison_data, key=lambda x: x["sharpe_ratio"]),
            "lowest_drawdown": min(comparison_data, key=lambda x: x["max_drawdown"]),
            "highest_win_rate": max(comparison_data, key=lambda x: x["win_rate"])
        }
    }
