# Database Configuration
DATABASE_URL=postgresql://markethawk:password@localhost:5432/markethawk
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your_influxdb_token
INFLUXDB_ORG=markethawk
INFLUXDB_BUCKET=market_data

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys - Free Tier
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
POLYGON_API_KEY=your_polygon_key
YAHOO_FINANCE_API_KEY=optional
FRED_API_KEY=your_fred_key
NEWS_API_KEY=your_news_api_key

# Broker API Keys (Sandbox/Paper Trading)
TD_AMERITRADE_CLIENT_ID=your_td_client_id
TD_AMERITRADE_REDIRECT_URI=http://localhost:8000/auth/td/callback
INTERACTIVE_BROKERS_HOST=localhost
INTERACTIVE_BROKERS_PORT=7497
ALPACA_API_KEY=your_alpaca_key
ALPACA_SECRET_KEY=your_alpaca_secret
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# Application Settings
DEBUG=true
ENVIRONMENT=development
LOG_LEVEL=INFO
CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Monitoring (Optional)
SENTRY_DSN=your_sentry_dsn
PROMETHEUS_PORT=9090

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Trading Configuration
DEFAULT_POSITION_SIZE=0.02
MAX_POSITION_SIZE=0.10
MAX_DAILY_LOSS=0.05
PAPER_TRADING=true
