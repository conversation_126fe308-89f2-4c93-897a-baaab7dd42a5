import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Tooltip,
  Skeleton,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  TrendingFlat,
  Refresh,
  Info,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface MarketIndex {
  symbol: string;
  name: string;
  value: number;
  change: number;
  changePercent: number;
  volume?: number;
}

interface MarketSector {
  name: string;
  change: number;
  changePercent: number;
  color: string;
}

const MarketOverview: React.FC = () => {
  const [indices, setIndices] = useState<MarketIndex[]>([]);
  const [sectors, setSectors] = useState<MarketSector[]>([]);
  const [marketStatus, setMarketStatus] = useState<string>('Open');
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    fetchMarketData();
    const interval = setInterval(fetchMarketData, 60000); // Update every minute
    return () => clearInterval(interval);
  }, []);

  const fetchMarketData = async () => {
    try {
      // Mock data - in real app, fetch from API
      const mockIndices: MarketIndex[] = [
        {
          symbol: 'SPY',
          name: 'S&P 500',
          value: 4185.47,
          change: 12.35,
          changePercent: 0.30,
          volume: 45678900,
        },
        {
          symbol: 'QQQ',
          name: 'NASDAQ 100',
          value: 350.82,
          change: -2.18,
          changePercent: -0.62,
          volume: 32145600,
        },
        {
          symbol: 'IWM',
          name: 'Russell 2000',
          value: 198.45,
          change: 1.87,
          changePercent: 0.95,
          volume: 18234500,
        },
        {
          symbol: 'DIA',
          name: 'Dow Jones',
          value: 340.25,
          change: 0.45,
          changePercent: 0.13,
          volume: 12456700,
        },
      ];

      const mockSectors: MarketSector[] = [
        { name: 'Technology', change: 1.2, changePercent: 1.2, color: '#00d4aa' },
        { name: 'Healthcare', change: 0.8, changePercent: 0.8, color: '#4fffdf' },
        { name: 'Financials', change: -0.3, changePercent: -0.3, color: '#ff6b35' },
        { name: 'Energy', change: 2.1, changePercent: 2.1, color: '#00a37c' },
        { name: 'Consumer Disc.', change: -0.7, changePercent: -0.7, color: '#c73e0a' },
        { name: 'Industrials', change: 0.5, changePercent: 0.5, color: '#2196f3' },
        { name: 'Materials', change: 1.8, changePercent: 1.8, color: '#4caf50' },
        { name: 'Utilities', change: -0.2, changePercent: -0.2, color: '#ff9800' },
      ];

      setIndices(mockIndices);
      setSectors(mockSectors);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch market data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTrendIcon = (change: number) => {
    if (change > 0) return <TrendingUp color="success" fontSize="small" />;
    if (change < 0) return <TrendingDown color="error" fontSize="small" />;
    return <TrendingFlat color="action" fontSize="small" />;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'success.main';
    if (change < 0) return 'error.main';
    return 'text.secondary';
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    }
    if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Skeleton variant="text" width={150} height={32} />
            <Skeleton variant="circular" width={40} height={40} />
          </Box>
          <Grid container spacing={2}>
            {[1, 2, 3, 4].map((index) => (
              <Grid item xs={6} md={3} key={index}>
                <Skeleton variant="rectangular" height={80} />
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="h6" fontWeight={600}>
              Market Overview
            </Typography>
            <Chip
              label={marketStatus}
              color={marketStatus === 'Open' ? 'success' : 'default'}
              size="small"
            />
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </Typography>
            <Tooltip title="Refresh Market Data">
              <IconButton size="small" onClick={fetchMarketData}>
                <Refresh />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Major Indices */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          {indices.map((index, i) => (
            <Grid item xs={6} md={3} key={index.symbol}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: i * 0.1 }}
              >
                <Card variant="outlined" sx={{ height: '100%' }}>
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        {index.name}
                      </Typography>
                      {getTrendIcon(index.change)}
                    </Box>
                    
                    <Typography variant="h6" fontWeight={600} sx={{ mb: 0.5 }}>
                      {index.value.toFixed(2)}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography
                        variant="body2"
                        sx={{ color: getChangeColor(index.change) }}
                      >
                        {index.change > 0 ? '+' : ''}{index.change.toFixed(2)}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: getChangeColor(index.change) }}
                      >
                        ({index.changePercent > 0 ? '+' : ''}{index.changePercent.toFixed(2)}%)
                      </Typography>
                    </Box>
                    
                    {index.volume && (
                      <Typography variant="caption" color="text.secondary">
                        Vol: {formatVolume(index.volume)}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Sector Performance */}
        <Box>
          <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 2 }}>
            Sector Performance
          </Typography>
          
          <Grid container spacing={1}>
            {sectors.map((sector, index) => (
              <Grid item xs={6} sm={4} md={3} key={sector.name}>
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 1,
                      backgroundColor: 'background.default',
                      border: '1px solid',
                      borderColor: 'divider',
                      '&:hover': {
                        backgroundColor: 'action.hover',
                      },
                    }}
                  >
                    <Typography variant="body2" fontWeight={500} sx={{ mb: 0.5 }}>
                      {sector.name}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography
                        variant="body2"
                        sx={{ color: getChangeColor(sector.change) }}
                      >
                        {sector.change > 0 ? '+' : ''}{sector.changePercent.toFixed(1)}%
                      </Typography>
                      
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          backgroundColor: sector.color,
                        }}
                      />
                    </Box>
                    
                    <LinearProgress
                      variant="determinate"
                      value={Math.min(Math.abs(sector.changePercent) * 20, 100)}
                      sx={{
                        mt: 0.5,
                        height: 3,
                        backgroundColor: 'action.hover',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: sector.color,
                        },
                      }}
                    />
                  </Box>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};

export default MarketOverview;
