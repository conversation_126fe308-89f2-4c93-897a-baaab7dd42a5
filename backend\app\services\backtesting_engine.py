"""
Backtesting Engine Service
Provides comprehensive backtesting functionality for trading strategies
"""

import asyncio
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from app.models.stock import Stock
from app.models.portfolio import Strategy
from app.services.data_provider import DataProvider
from app.utils.indicators import TechnicalIndicators
from app.core.config import settings

logger = logging.getLogger(__name__)


@dataclass
class BacktestTrade:
    """Individual trade in backtest"""
    symbol: str
    entry_date: datetime
    exit_date: Optional[datetime]
    entry_price: float
    exit_price: Optional[float]
    quantity: int
    side: str  # "buy" or "sell"
    pnl: Optional[float]
    pnl_percent: Optional[float]
    commission: float
    duration_days: Optional[int]
    entry_reason: str
    exit_reason: Optional[str]


@dataclass
class BacktestResults:
    """Comprehensive backtest results"""
    # Basic metrics
    initial_capital: float
    final_capital: float
    total_return: float
    total_return_percent: float
    annualized_return: float
    
    # Risk metrics
    max_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    volatility: float
    beta: float
    alpha: float
    
    # Trade statistics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    
    # Benchmark comparison
    benchmark_return: float
    benchmark_volatility: float
    
    # Detailed data
    trades: List[BacktestTrade]
    equity_curve: List[Dict[str, Any]]
    monthly_returns: Dict[str, float]
    position_history: List[Dict[str, Any]]
    risk_metrics: Dict[str, float]


class BacktestingEngine:
    """
    Advanced backtesting engine for trading strategies
    Supports multiple position sizing methods and realistic execution
    """
    
    def __init__(self):
        self.data_provider = DataProvider()
        self.technical_indicators = TechnicalIndicators()
    
    async def run_backtest(
        self,
        strategy: Strategy,
        start_date: datetime,
        end_date: datetime,
        initial_capital: float = 100000,
        commission_per_trade: float = 1.0,
        slippage_percent: float = 0.1,
        symbols: Optional[List[str]] = None,
        benchmark_symbol: str = "SPY",
        max_positions: int = 10,
        position_sizing: str = "equal_weight",
        rebalance_frequency: str = "monthly"
    ) -> Dict[str, Any]:
        """
        Run comprehensive backtest for a strategy
        
        Args:
            strategy: Trading strategy to backtest
            start_date: Backtest start date
            end_date: Backtest end date
            initial_capital: Starting capital
            commission_per_trade: Commission per trade
            slippage_percent: Slippage percentage
            symbols: List of symbols to test (None = use strategy universe)
            benchmark_symbol: Benchmark for comparison
            max_positions: Maximum concurrent positions
            position_sizing: Position sizing method
            rebalance_frequency: Rebalancing frequency
            
        Returns:
            Comprehensive backtest results
        """
        try:
            logger.info(f"Starting backtest for strategy {strategy.name}")
            
            # Get symbols to test
            test_symbols = symbols or await self._get_strategy_universe(strategy)
            
            # Get historical data
            historical_data = await self._get_historical_data(
                test_symbols, start_date, end_date
            )
            
            if not historical_data:
                raise ValueError("No historical data available for backtest")
            
            # Get benchmark data
            benchmark_data = await self._get_historical_data(
                [benchmark_symbol], start_date, end_date
            )
            
            # Initialize backtest state
            backtest_state = {
                "capital": initial_capital,
                "positions": {},  # symbol -> position info
                "trades": [],
                "equity_curve": [],
                "daily_returns": [],
                "benchmark_returns": []
            }
            
            # Run day-by-day simulation
            current_date = start_date
            trading_days = pd.bdate_range(start_date, end_date)
            
            for i, date in enumerate(trading_days):
                # Update positions with current prices
                await self._update_positions(backtest_state, historical_data, date)
                
                # Calculate portfolio value
                portfolio_value = await self._calculate_portfolio_value(
                    backtest_state, historical_data, date
                )
                
                # Record equity curve point
                benchmark_value = self._get_benchmark_value(benchmark_data, benchmark_symbol, date)
                daily_return = 0.0
                
                if i > 0:
                    prev_value = backtest_state["equity_curve"][-1]["portfolio_value"]
                    daily_return = (portfolio_value - prev_value) / prev_value if prev_value > 0 else 0
                
                backtest_state["equity_curve"].append({
                    "date": date.isoformat(),
                    "portfolio_value": portfolio_value,
                    "benchmark_value": benchmark_value,
                    "drawdown": self._calculate_drawdown(backtest_state["equity_curve"], portfolio_value),
                    "daily_return": daily_return,
                    "cumulative_return": (portfolio_value - initial_capital) / initial_capital
                })
                
                backtest_state["daily_returns"].append(daily_return)
                
                # Check for rebalancing
                should_rebalance = self._should_rebalance(date, rebalance_frequency, i)
                
                if should_rebalance:
                    # Run screening for this date
                    screening_results = await self._run_screening_for_date(
                        strategy, historical_data, date
                    )
                    
                    # Generate trades based on screening results
                    new_trades = await self._generate_trades(
                        backtest_state,
                        screening_results,
                        historical_data,
                        date,
                        max_positions,
                        position_sizing,
                        commission_per_trade,
                        slippage_percent
                    )
                    
                    backtest_state["trades"].extend(new_trades)
                
                # Check for exit conditions
                exit_trades = await self._check_exit_conditions(
                    backtest_state, strategy, historical_data, date, commission_per_trade, slippage_percent
                )
                
                backtest_state["trades"].extend(exit_trades)
            
            # Calculate final results
            results = await self._calculate_backtest_results(
                backtest_state,
                initial_capital,
                start_date,
                end_date,
                benchmark_data,
                benchmark_symbol
            )
            
            logger.info(f"Backtest completed for strategy {strategy.name}")
            return results
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            raise
    
    async def _get_strategy_universe(self, strategy: Strategy) -> List[str]:
        """Get symbols for strategy universe"""
        if not strategy.universe:
            return ["SPY", "QQQ", "IWM"]  # Default universe
        
        symbols = []
        for universe_item in strategy.universe:
            if universe_item == "SP500":
                sp500_symbols = await self.data_provider.get_sp500_symbols()
                symbols.extend(sp500_symbols[:100])  # Limit for backtesting
            elif universe_item == "NASDAQ100":
                nasdaq_symbols = await self.data_provider.get_nasdaq100_symbols()
                symbols.extend(nasdaq_symbols[:50])
            else:
                symbols.append(universe_item)
        
        return list(set(symbols))  # Remove duplicates
    
    async def _get_historical_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, pd.DataFrame]:
        """Get historical data for symbols"""
        historical_data = {}
        
        for symbol in symbols:
            try:
                # Get daily OHLCV data
                data = await self.data_provider.get_historical_data(
                    symbol, start_date, end_date, "1d"
                )
                
                if data is not None and not data.empty:
                    historical_data[symbol] = data
                    
            except Exception as e:
                logger.warning(f"Could not get historical data for {symbol}: {e}")
                continue
        
        return historical_data
    
    async def _update_positions(
        self,
        backtest_state: Dict[str, Any],
        historical_data: Dict[str, pd.DataFrame],
        date: datetime
    ):
        """Update position values with current market prices"""
        for symbol, position in backtest_state["positions"].items():
            if symbol in historical_data:
                data = historical_data[symbol]
                date_str = date.strftime('%Y-%m-%d')
                
                if date_str in data.index:
                    current_price = data.loc[date_str, 'close']
                    position["current_price"] = current_price
                    position["market_value"] = position["quantity"] * current_price
                    position["unrealized_pnl"] = position["market_value"] - position["cost_basis"]
    
    async def _calculate_portfolio_value(
        self,
        backtest_state: Dict[str, Any],
        historical_data: Dict[str, pd.DataFrame],
        date: datetime
    ) -> float:
        """Calculate total portfolio value"""
        cash = backtest_state["capital"]
        positions_value = sum(
            pos.get("market_value", 0) for pos in backtest_state["positions"].values()
        )
        return cash + positions_value
    
    def _get_benchmark_value(
        self,
        benchmark_data: Dict[str, pd.DataFrame],
        benchmark_symbol: str,
        date: datetime
    ) -> float:
        """Get benchmark value for date"""
        if benchmark_symbol not in benchmark_data:
            return 100.0  # Default value
        
        data = benchmark_data[benchmark_symbol]
        date_str = date.strftime('%Y-%m-%d')
        
        if date_str in data.index:
            return data.loc[date_str, 'close']
        
        return 100.0
    
    def _calculate_drawdown(self, equity_curve: List[Dict], current_value: float) -> float:
        """Calculate current drawdown"""
        if not equity_curve:
            return 0.0
        
        peak_value = max(point["portfolio_value"] for point in equity_curve)
        peak_value = max(peak_value, current_value)
        
        if peak_value == 0:
            return 0.0
        
        return (peak_value - current_value) / peak_value
    
    def _should_rebalance(self, date: datetime, frequency: str, day_index: int) -> bool:
        """Check if portfolio should be rebalanced"""
        if frequency == "daily":
            return True
        elif frequency == "weekly":
            return date.weekday() == 0  # Monday
        elif frequency == "monthly":
            return date.day == 1 or day_index == 0  # First day of month or first day
        elif frequency == "quarterly":
            return date.month in [1, 4, 7, 10] and date.day == 1
        
        return False

    async def _run_screening_for_date(
        self,
        strategy: Strategy,
        historical_data: Dict[str, pd.DataFrame],
        date: datetime
    ) -> List[Dict[str, Any]]:
        """Run strategy screening for a specific date"""
        screening_results = []

        for symbol, data in historical_data.items():
            try:
                date_str = date.strftime('%Y-%m-%d')

                if date_str not in data.index:
                    continue

                # Get data up to current date for indicators
                historical_subset = data.loc[:date_str]

                if len(historical_subset) < 50:  # Need enough data for indicators
                    continue

                # Calculate technical indicators
                indicators = await self.technical_indicators.calculate_all_indicators(
                    historical_subset
                )

                # Evaluate screening criteria
                score = await self._evaluate_screening_criteria(
                    strategy.screening_criteria,
                    historical_subset.iloc[-1],  # Current day data
                    indicators
                )

                if score >= strategy.min_score:
                    screening_results.append({
                        "symbol": symbol,
                        "score": score,
                        "price": historical_subset.iloc[-1]['close'],
                        "volume": historical_subset.iloc[-1]['volume'],
                        "indicators": indicators
                    })

            except Exception as e:
                logger.warning(f"Error screening {symbol} for {date}: {e}")
                continue

        # Sort by score and limit results
        screening_results.sort(key=lambda x: x["score"], reverse=True)
        return screening_results[:strategy.max_results]

    async def _evaluate_screening_criteria(
        self,
        criteria: List[Dict[str, Any]],
        market_data: pd.Series,
        indicators: Dict[str, float]
    ) -> float:
        """Evaluate screening criteria and return score"""
        total_score = 0.0
        total_weight = 0.0

        for criterion in criteria:
            field = criterion["field"]
            operator = criterion["operator"]
            value = criterion["value"]
            weight = criterion.get("weight", 1.0)

            # Get field value
            field_value = None
            if field in market_data.index:
                field_value = market_data[field]
            elif field in indicators:
                field_value = indicators[field]
            elif field == "price":
                field_value = market_data.get("close")
            elif field == "volume":
                field_value = market_data.get("volume")

            if field_value is None:
                continue

            # Evaluate criterion
            criterion_met = False

            if operator == ">":
                criterion_met = field_value > value
            elif operator == "<":
                criterion_met = field_value < value
            elif operator == ">=":
                criterion_met = field_value >= value
            elif operator == "<=":
                criterion_met = field_value <= value
            elif operator == "==":
                criterion_met = abs(field_value - value) < 0.01
            elif operator == "!=":
                criterion_met = abs(field_value - value) >= 0.01
            elif operator == "between" and isinstance(value, list) and len(value) == 2:
                criterion_met = value[0] <= field_value <= value[1]

            if criterion_met:
                total_score += weight * 100

            total_weight += weight

        return (total_score / total_weight) if total_weight > 0 else 0.0

    async def _generate_trades(
        self,
        backtest_state: Dict[str, Any],
        screening_results: List[Dict[str, Any]],
        historical_data: Dict[str, pd.DataFrame],
        date: datetime,
        max_positions: int,
        position_sizing: str,
        commission: float,
        slippage_percent: float
    ) -> List[BacktestTrade]:
        """Generate trades based on screening results"""
        new_trades = []
        current_positions = len(backtest_state["positions"])

        for result in screening_results:
            if current_positions >= max_positions:
                break

            symbol = result["symbol"]

            # Skip if already have position
            if symbol in backtest_state["positions"]:
                continue

            # Calculate position size
            position_size = self._calculate_position_size(
                backtest_state["capital"],
                result["price"],
                position_sizing,
                max_positions
            )

            if position_size <= 0:
                continue

            # Apply slippage
            entry_price = result["price"] * (1 + slippage_percent / 100)
            total_cost = position_size * entry_price + commission

            # Check if we have enough capital
            if total_cost > backtest_state["capital"]:
                continue

            # Execute trade
            backtest_state["capital"] -= total_cost
            backtest_state["positions"][symbol] = {
                "quantity": position_size,
                "entry_price": entry_price,
                "entry_date": date,
                "cost_basis": total_cost,
                "current_price": entry_price,
                "market_value": position_size * entry_price,
                "unrealized_pnl": 0.0
            }

            # Record trade
            trade = BacktestTrade(
                symbol=symbol,
                entry_date=date,
                exit_date=None,
                entry_price=entry_price,
                exit_price=None,
                quantity=position_size,
                side="buy",
                pnl=None,
                pnl_percent=None,
                commission=commission,
                duration_days=None,
                entry_reason=f"Screening score: {result['score']:.1f}",
                exit_reason=None
            )

            new_trades.append(trade)
            current_positions += 1

        return new_trades

    def _calculate_position_size(
        self,
        available_capital: float,
        price: float,
        sizing_method: str,
        max_positions: int
    ) -> int:
        """Calculate position size based on sizing method"""
        if sizing_method == "equal_weight":
            # Equal weight across max positions
            position_value = available_capital / max_positions
            return int(position_value / price)

        elif sizing_method == "risk_parity":
            # Simplified risk parity (would use volatility in production)
            position_value = available_capital * 0.05  # 5% per position
            return int(position_value / price)

        elif sizing_method == "kelly":
            # Simplified Kelly criterion (would use win rate and avg returns)
            kelly_fraction = 0.1  # 10% of capital
            position_value = available_capital * kelly_fraction
            return int(position_value / price)

        else:
            # Default to equal weight
            position_value = available_capital / max_positions
            return int(position_value / price)

    async def _check_exit_conditions(
        self,
        backtest_state: Dict[str, Any],
        strategy: Strategy,
        historical_data: Dict[str, pd.DataFrame],
        date: datetime,
        commission: float,
        slippage_percent: float
    ) -> List[BacktestTrade]:
        """Check for exit conditions and generate exit trades"""
        exit_trades = []
        positions_to_remove = []

        for symbol, position in backtest_state["positions"].items():
            if symbol not in historical_data:
                continue

            data = historical_data[symbol]
            date_str = date.strftime('%Y-%m-%d')

            if date_str not in data.index:
                continue

            current_price = data.loc[date_str, 'close']
            entry_price = position["entry_price"]

            # Check stop loss
            stop_loss_triggered = False
            if strategy.stop_loss_percent:
                stop_loss_price = entry_price * (1 - strategy.stop_loss_percent)
                if current_price <= stop_loss_price:
                    stop_loss_triggered = True

            # Check take profit
            take_profit_triggered = False
            if strategy.take_profit_percent:
                take_profit_price = entry_price * (1 + strategy.take_profit_percent)
                if current_price >= take_profit_price:
                    take_profit_triggered = True

            # Check time-based exit (hold for max 30 days)
            days_held = (date - position["entry_date"]).days
            time_exit_triggered = days_held >= 30

            if stop_loss_triggered or take_profit_triggered or time_exit_triggered:
                # Execute exit trade
                exit_price = current_price * (1 - slippage_percent / 100)  # Slippage on exit
                quantity = position["quantity"]
                gross_proceeds = quantity * exit_price
                net_proceeds = gross_proceeds - commission

                # Update capital
                backtest_state["capital"] += net_proceeds

                # Calculate P&L
                pnl = net_proceeds - position["cost_basis"]
                pnl_percent = (pnl / position["cost_basis"]) * 100 if position["cost_basis"] > 0 else 0

                # Determine exit reason
                exit_reason = "Time exit"
                if stop_loss_triggered:
                    exit_reason = "Stop loss"
                elif take_profit_triggered:
                    exit_reason = "Take profit"

                # Create exit trade
                exit_trade = BacktestTrade(
                    symbol=symbol,
                    entry_date=position["entry_date"],
                    exit_date=date,
                    entry_price=position["entry_price"],
                    exit_price=exit_price,
                    quantity=quantity,
                    side="sell",
                    pnl=pnl,
                    pnl_percent=pnl_percent,
                    commission=commission,
                    duration_days=days_held,
                    entry_reason="",
                    exit_reason=exit_reason
                )

                exit_trades.append(exit_trade)
                positions_to_remove.append(symbol)

        # Remove closed positions
        for symbol in positions_to_remove:
            del backtest_state["positions"][symbol]

        return exit_trades

    async def _calculate_backtest_results(
        self,
        backtest_state: Dict[str, Any],
        initial_capital: float,
        start_date: datetime,
        end_date: datetime,
        benchmark_data: Dict[str, pd.DataFrame],
        benchmark_symbol: str
    ) -> Dict[str, Any]:
        """Calculate comprehensive backtest results"""

        # Basic metrics
        final_capital = backtest_state["equity_curve"][-1]["portfolio_value"] if backtest_state["equity_curve"] else initial_capital
        total_return = final_capital - initial_capital
        total_return_percent = (total_return / initial_capital) * 100 if initial_capital > 0 else 0

        # Annualized return
        days = (end_date - start_date).days
        years = days / 365.25
        annualized_return = ((final_capital / initial_capital) ** (1 / years) - 1) * 100 if years > 0 and initial_capital > 0 else 0

        # Trade statistics
        completed_trades = [t for t in backtest_state["trades"] if t.exit_date is not None]
        total_trades = len(completed_trades)
        winning_trades = len([t for t in completed_trades if (t.pnl or 0) > 0])
        losing_trades = len([t for t in completed_trades if (t.pnl or 0) < 0])

        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

        # P&L statistics
        wins = [t.pnl for t in completed_trades if (t.pnl or 0) > 0]
        losses = [abs(t.pnl) for t in completed_trades if (t.pnl or 0) < 0]

        avg_win = sum(wins) / len(wins) if wins else 0
        avg_loss = sum(losses) / len(losses) if losses else 0
        profit_factor = sum(wins) / sum(losses) if losses and sum(losses) > 0 else 0

        # Consecutive wins/losses
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_wins = 0
        current_losses = 0

        for trade in completed_trades:
            if (trade.pnl or 0) > 0:
                current_wins += 1
                current_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, current_losses)

        # Risk metrics
        daily_returns = backtest_state["daily_returns"]

        if daily_returns:
            volatility = np.std(daily_returns) * np.sqrt(252) * 100  # Annualized volatility

            # Sharpe ratio (assuming 2% risk-free rate)
            risk_free_rate = 0.02
            excess_returns = np.array(daily_returns) - (risk_free_rate / 252)
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0

            # Sortino ratio (downside deviation)
            downside_returns = [r for r in daily_returns if r < 0]
            downside_deviation = np.std(downside_returns) if downside_returns else 0
            sortino_ratio = np.mean(excess_returns) / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0
        else:
            volatility = 0
            sharpe_ratio = 0
            sortino_ratio = 0

        # Maximum drawdown
        max_drawdown = 0
        if backtest_state["equity_curve"]:
            peak = initial_capital
            for point in backtest_state["equity_curve"]:
                value = point["portfolio_value"]
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak if peak > 0 else 0
                max_drawdown = max(max_drawdown, drawdown)

        max_drawdown *= 100  # Convert to percentage

        # Calmar ratio
        calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0

        # Benchmark comparison
        benchmark_return = 0
        benchmark_volatility = 0
        beta = 1.0
        alpha = 0

        if benchmark_symbol in benchmark_data:
            benchmark_df = benchmark_data[benchmark_symbol]
            if not benchmark_df.empty:
                start_price = benchmark_df.iloc[0]['close']
                end_price = benchmark_df.iloc[-1]['close']
                benchmark_return = ((end_price / start_price) - 1) * 100 if start_price > 0 else 0

                benchmark_returns = benchmark_df['close'].pct_change().dropna()
                benchmark_volatility = benchmark_returns.std() * np.sqrt(252) * 100

                # Beta calculation (simplified)
                if len(daily_returns) == len(benchmark_returns):
                    covariance = np.cov(daily_returns, benchmark_returns)[0][1]
                    benchmark_variance = np.var(benchmark_returns)
                    beta = covariance / benchmark_variance if benchmark_variance > 0 else 1.0

                    # Alpha calculation
                    alpha = annualized_return - (risk_free_rate * 100 + beta * (benchmark_return - risk_free_rate * 100))

        # Monthly returns
        monthly_returns = {}
        if backtest_state["equity_curve"]:
            monthly_data = {}
            for point in backtest_state["equity_curve"]:
                date_obj = datetime.fromisoformat(point["date"])
                month_key = date_obj.strftime("%Y-%m")

                if month_key not in monthly_data:
                    monthly_data[month_key] = {"start": point["portfolio_value"], "end": point["portfolio_value"]}
                else:
                    monthly_data[month_key]["end"] = point["portfolio_value"]

            for month, data in monthly_data.items():
                if data["start"] > 0:
                    monthly_returns[month] = ((data["end"] / data["start"]) - 1) * 100

        # Position history
        position_history = []
        for trade in completed_trades:
            position_history.append({
                "symbol": trade.symbol,
                "entry_date": trade.entry_date.isoformat(),
                "exit_date": trade.exit_date.isoformat() if trade.exit_date else None,
                "quantity": trade.quantity,
                "entry_price": trade.entry_price,
                "exit_price": trade.exit_price,
                "pnl": trade.pnl,
                "pnl_percent": trade.pnl_percent,
                "duration_days": trade.duration_days
            })

        # Additional risk metrics
        risk_metrics = {
            "value_at_risk_95": np.percentile(daily_returns, 5) * final_capital if daily_returns else 0,
            "expected_shortfall": np.mean([r for r in daily_returns if r <= np.percentile(daily_returns, 5)]) * final_capital if daily_returns else 0,
            "skewness": float(pd.Series(daily_returns).skew()) if daily_returns else 0,
            "kurtosis": float(pd.Series(daily_returns).kurtosis()) if daily_returns else 0,
            "max_daily_gain": max(daily_returns) * 100 if daily_returns else 0,
            "max_daily_loss": min(daily_returns) * 100 if daily_returns else 0
        }

        return {
            "initial_capital": initial_capital,
            "final_capital": final_capital,
            "total_return": total_return,
            "total_return_percent": total_return_percent,
            "annualized_return": annualized_return,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "sortino_ratio": sortino_ratio,
            "calmar_ratio": calmar_ratio,
            "volatility": volatility,
            "beta": beta,
            "alpha": alpha,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "max_consecutive_wins": max_consecutive_wins,
            "max_consecutive_losses": max_consecutive_losses,
            "benchmark_return": benchmark_return,
            "benchmark_volatility": benchmark_volatility,
            "trades": [
                {
                    "symbol": t.symbol,
                    "entry_date": t.entry_date.isoformat(),
                    "exit_date": t.exit_date.isoformat() if t.exit_date else None,
                    "entry_price": t.entry_price,
                    "exit_price": t.exit_price,
                    "quantity": t.quantity,
                    "side": t.side,
                    "pnl": t.pnl,
                    "pnl_percent": t.pnl_percent,
                    "commission": t.commission,
                    "duration_days": t.duration_days,
                    "entry_reason": t.entry_reason,
                    "exit_reason": t.exit_reason
                }
                for t in backtest_state["trades"]
            ],
            "equity_curve": backtest_state["equity_curve"],
            "monthly_returns": monthly_returns,
            "position_history": position_history,
            "risk_metrics": risk_metrics
        }
