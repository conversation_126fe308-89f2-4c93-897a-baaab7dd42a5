"""Initial migration

Revision ID: 0c009f72cb56
Revises: 
Create Date: 2025-07-13 14:06:24.908375

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0c009f72cb56'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('broker_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('broker_name', sa.String(length=50), nullable=False),
    sa.Column('account_id', sa.String(length=100), nullable=False),
    sa.Column('account_name', sa.String(length=100), nullable=True),
    sa.Column('encrypted_api_key', sa.Text(), nullable=True),
    sa.Column('encrypted_secret_key', sa.Text(), nullable=True),
    sa.Column('encrypted_access_token', sa.Text(), nullable=True),
    sa.Column('encrypted_refresh_token', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_paper_trading', sa.Boolean(), nullable=True),
    sa.Column('max_position_size', sa.Float(), nullable=True),
    sa.Column('max_daily_loss', sa.Float(), nullable=True),
    sa.Column('allowed_symbols', sa.JSON(), nullable=True),
    sa.Column('last_connected', sa.DateTime(timezone=True), nullable=True),
    sa.Column('connection_status', sa.String(length=20), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_broker_accounts'))
    )
    with op.batch_alter_table('broker_accounts', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_broker_accounts_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_broker_accounts_user_id'), ['user_id'], unique=False)

    op.create_table('market_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=10), nullable=False),
    sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
    sa.Column('open_price', sa.Float(), nullable=False),
    sa.Column('high_price', sa.Float(), nullable=False),
    sa.Column('low_price', sa.Float(), nullable=False),
    sa.Column('close_price', sa.Float(), nullable=False),
    sa.Column('volume', sa.Integer(), nullable=False),
    sa.Column('adjusted_close', sa.Float(), nullable=True),
    sa.Column('timeframe', sa.String(length=10), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_market_data'))
    )
    with op.batch_alter_table('market_data', schema=None) as batch_op:
        batch_op.create_index('idx_market_data_symbol_timestamp', ['symbol', 'timestamp'], unique=False)
        batch_op.create_index('idx_market_data_timeframe', ['timeframe'], unique=False)
        batch_op.create_index(batch_op.f('ix_market_data_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_market_data_symbol'), ['symbol'], unique=False)

    op.create_table('positions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('stock_id', sa.Integer(), nullable=False),
    sa.Column('broker_account_id', sa.Integer(), nullable=False),
    sa.Column('portfolio_id', sa.Integer(), nullable=True),
    sa.Column('position_type', sa.Enum('LONG', 'SHORT', name='positiontype'), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('average_cost', sa.Float(), nullable=False),
    sa.Column('current_price', sa.Float(), nullable=True),
    sa.Column('market_value', sa.Float(), nullable=True),
    sa.Column('unrealized_pnl', sa.Float(), nullable=True),
    sa.Column('unrealized_pnl_percent', sa.Float(), nullable=True),
    sa.Column('day_pnl', sa.Float(), nullable=True),
    sa.Column('day_pnl_percent', sa.Float(), nullable=True),
    sa.Column('stop_loss_price', sa.Float(), nullable=True),
    sa.Column('take_profit_price', sa.Float(), nullable=True),
    sa.Column('opened_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('last_updated', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('is_paper_position', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_positions'))
    )
    with op.batch_alter_table('positions', schema=None) as batch_op:
        batch_op.create_index('idx_position_broker_account', ['broker_account_id'], unique=False)
        batch_op.create_index('idx_position_user_stock', ['user_id', 'stock_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_positions_broker_account_id'), ['broker_account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_positions_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_positions_portfolio_id'), ['portfolio_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_positions_stock_id'), ['stock_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_positions_user_id'), ['user_id'], unique=False)

    op.create_table('screening_results',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('strategy_id', sa.Integer(), nullable=False),
    sa.Column('stock_id', sa.Integer(), nullable=False),
    sa.Column('scan_timestamp', sa.DateTime(timezone=True), nullable=False),
    sa.Column('criteria_met', sa.JSON(), nullable=False),
    sa.Column('score', sa.Float(), nullable=False),
    sa.Column('signal_strength', sa.String(length=20), nullable=False),
    sa.Column('entry_price', sa.Float(), nullable=True),
    sa.Column('target_price', sa.Float(), nullable=True),
    sa.Column('stop_loss_price', sa.Float(), nullable=True),
    sa.Column('market_data', sa.JSON(), nullable=True),
    sa.Column('action_taken', sa.String(length=20), nullable=True),
    sa.Column('trade_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_screening_results'))
    )
    with op.batch_alter_table('screening_results', schema=None) as batch_op:
        batch_op.create_index('idx_screening_result_score', ['score'], unique=False)
        batch_op.create_index('idx_screening_result_signal', ['signal_strength'], unique=False)
        batch_op.create_index('idx_screening_result_strategy_timestamp', ['strategy_id', 'scan_timestamp'], unique=False)
        batch_op.create_index(batch_op.f('ix_screening_results_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_screening_results_stock_id'), ['stock_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_screening_results_strategy_id'), ['strategy_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_screening_results_trade_id'), ['trade_id'], unique=False)

    op.create_table('stocks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=10), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('exchange', sa.String(length=10), nullable=False),
    sa.Column('sector', sa.String(length=100), nullable=True),
    sa.Column('industry', sa.String(length=100), nullable=True),
    sa.Column('market_cap_category', sa.String(length=20), nullable=True),
    sa.Column('current_price', sa.Float(), nullable=True),
    sa.Column('previous_close', sa.Float(), nullable=True),
    sa.Column('day_change', sa.Float(), nullable=True),
    sa.Column('day_change_percent', sa.Float(), nullable=True),
    sa.Column('volume', sa.Integer(), nullable=True),
    sa.Column('average_volume', sa.Integer(), nullable=True),
    sa.Column('bid_price', sa.Float(), nullable=True),
    sa.Column('ask_price', sa.Float(), nullable=True),
    sa.Column('bid_size', sa.Integer(), nullable=True),
    sa.Column('ask_size', sa.Integer(), nullable=True),
    sa.Column('market_cap', sa.Float(), nullable=True),
    sa.Column('shares_outstanding', sa.Integer(), nullable=True),
    sa.Column('float_shares', sa.Integer(), nullable=True),
    sa.Column('pe_ratio', sa.Float(), nullable=True),
    sa.Column('pb_ratio', sa.Float(), nullable=True),
    sa.Column('ps_ratio', sa.Float(), nullable=True),
    sa.Column('peg_ratio', sa.Float(), nullable=True),
    sa.Column('dividend_yield', sa.Float(), nullable=True),
    sa.Column('eps', sa.Float(), nullable=True),
    sa.Column('revenue', sa.Float(), nullable=True),
    sa.Column('rsi_14', sa.Float(), nullable=True),
    sa.Column('sma_20', sa.Float(), nullable=True),
    sa.Column('sma_50', sa.Float(), nullable=True),
    sa.Column('sma_200', sa.Float(), nullable=True),
    sa.Column('ema_12', sa.Float(), nullable=True),
    sa.Column('ema_26', sa.Float(), nullable=True),
    sa.Column('macd', sa.Float(), nullable=True),
    sa.Column('macd_signal', sa.Float(), nullable=True),
    sa.Column('bollinger_upper', sa.Float(), nullable=True),
    sa.Column('bollinger_lower', sa.Float(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_tradeable', sa.Boolean(), nullable=True),
    sa.Column('last_updated', sa.DateTime(timezone=True), nullable=True),
    sa.Column('additional_data', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_stocks'))
    )
    with op.batch_alter_table('stocks', schema=None) as batch_op:
        batch_op.create_index('idx_stock_market_cap', ['market_cap'], unique=False)
        batch_op.create_index('idx_stock_price', ['current_price'], unique=False)
        batch_op.create_index('idx_stock_sector_industry', ['sector', 'industry'], unique=False)
        batch_op.create_index('idx_stock_volume', ['volume'], unique=False)
        batch_op.create_index(batch_op.f('ix_stocks_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_stocks_symbol'), ['symbol'], unique=True)

    op.create_table('trade_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('stock_id', sa.Integer(), nullable=False),
    sa.Column('strategy_id', sa.Integer(), nullable=True),
    sa.Column('entry_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('exit_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('position_type', sa.Enum('LONG', 'SHORT', name='positiontype'), nullable=False),
    sa.Column('entry_price', sa.Float(), nullable=False),
    sa.Column('entry_quantity', sa.Integer(), nullable=False),
    sa.Column('entry_commission', sa.Float(), nullable=True),
    sa.Column('exit_price', sa.Float(), nullable=False),
    sa.Column('exit_quantity', sa.Integer(), nullable=False),
    sa.Column('exit_commission', sa.Float(), nullable=True),
    sa.Column('gross_pnl', sa.Float(), nullable=False),
    sa.Column('net_pnl', sa.Float(), nullable=False),
    sa.Column('pnl_percent', sa.Float(), nullable=False),
    sa.Column('hold_duration_days', sa.Float(), nullable=True),
    sa.Column('is_winning_trade', sa.Boolean(), nullable=True),
    sa.Column('is_paper_trade', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_trade_history'))
    )
    with op.batch_alter_table('trade_history', schema=None) as batch_op:
        batch_op.create_index('idx_trade_history_performance', ['is_winning_trade', 'pnl_percent'], unique=False)
        batch_op.create_index('idx_trade_history_user_date', ['user_id', 'exit_date'], unique=False)
        batch_op.create_index(batch_op.f('ix_trade_history_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trade_history_stock_id'), ['stock_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trade_history_strategy_id'), ['strategy_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trade_history_user_id'), ['user_id'], unique=False)

    op.create_table('trades',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('stock_id', sa.Integer(), nullable=False),
    sa.Column('broker_account_id', sa.Integer(), nullable=False),
    sa.Column('portfolio_id', sa.Integer(), nullable=True),
    sa.Column('strategy_id', sa.Integer(), nullable=True),
    sa.Column('broker_order_id', sa.String(length=100), nullable=True),
    sa.Column('order_type', sa.Enum('MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT', 'TRAILING_STOP', name='ordertype'), nullable=False),
    sa.Column('order_side', sa.Enum('BUY', 'SELL', 'BUY_TO_COVER', 'SELL_SHORT', name='orderside'), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('stop_price', sa.Float(), nullable=True),
    sa.Column('filled_quantity', sa.Integer(), nullable=True),
    sa.Column('average_fill_price', sa.Float(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'SUBMITTED', 'PARTIALLY_FILLED', 'FILLED', 'CANCELLED', 'REJECTED', 'EXPIRED', name='orderstatus'), nullable=True),
    sa.Column('rejection_reason', sa.Text(), nullable=True),
    sa.Column('time_in_force', sa.String(length=10), nullable=True),
    sa.Column('submitted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('filled_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('cancelled_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('commission', sa.Float(), nullable=True),
    sa.Column('fees', sa.Float(), nullable=True),
    sa.Column('total_cost', sa.Float(), nullable=True),
    sa.Column('realized_pnl', sa.Float(), nullable=True),
    sa.Column('stop_loss_price', sa.Float(), nullable=True),
    sa.Column('take_profit_price', sa.Float(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('broker_metadata', sa.JSON(), nullable=True),
    sa.Column('is_paper_trade', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_trades'))
    )
    with op.batch_alter_table('trades', schema=None) as batch_op:
        batch_op.create_index('idx_trade_broker_order', ['broker_order_id'], unique=False)
        batch_op.create_index('idx_trade_status', ['status'], unique=False)
        batch_op.create_index('idx_trade_user_date', ['user_id', 'created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_trades_broker_account_id'), ['broker_account_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trades_broker_order_id'), ['broker_order_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trades_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trades_portfolio_id'), ['portfolio_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trades_stock_id'), ['stock_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trades_strategy_id'), ['strategy_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_trades_user_id'), ['user_id'], unique=False)

    op.create_table('user_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_token', sa.String(length=255), nullable=False),
    sa.Column('refresh_token', sa.String(length=255), nullable=True),
    sa.Column('device_info', sa.JSON(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('last_activity', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_user_sessions'))
    )
    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_sessions_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_sessions_refresh_token'), ['refresh_token'], unique=True)
        batch_op.create_index(batch_op.f('ix_user_sessions_session_token'), ['session_token'], unique=True)
        batch_op.create_index(batch_op.f('ix_user_sessions_user_id'), ['user_id'], unique=False)

    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('first_name', sa.String(length=100), nullable=True),
    sa.Column('last_name', sa.String(length=100), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('is_admin', sa.Boolean(), nullable=True),
    sa.Column('api_key_hash', sa.String(length=255), nullable=True),
    sa.Column('api_key_created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('subscription_tier', sa.String(length=20), nullable=True),
    sa.Column('max_portfolios', sa.Integer(), nullable=True),
    sa.Column('max_strategies', sa.Integer(), nullable=True),
    sa.Column('max_api_calls_per_day', sa.Integer(), nullable=True),
    sa.Column('default_position_size', sa.Float(), nullable=True),
    sa.Column('risk_tolerance', sa.String(length=20), nullable=True),
    sa.Column('paper_trading_only', sa.Boolean(), nullable=True),
    sa.Column('email_notifications', sa.Boolean(), nullable=True),
    sa.Column('sms_notifications', sa.Boolean(), nullable=True),
    sa.Column('push_notifications', sa.Boolean(), nullable=True),
    sa.Column('settings', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_users'))
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_api_key_hash'), ['api_key_hash'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=True)

    op.create_table('watchlist_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('watchlist_id', sa.Integer(), nullable=False),
    sa.Column('stock_id', sa.Integer(), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('target_price', sa.Float(), nullable=True),
    sa.Column('stop_loss_price', sa.Float(), nullable=True),
    sa.Column('price_alert_enabled', sa.Boolean(), nullable=True),
    sa.Column('volume_alert_enabled', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_watchlist_items'))
    )
    with op.batch_alter_table('watchlist_items', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_watchlist_items_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_watchlist_items_stock_id'), ['stock_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_watchlist_items_watchlist_id'), ['watchlist_id'], unique=False)

    op.create_table('watchlists',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_watchlists'))
    )
    with op.batch_alter_table('watchlists', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_watchlists_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_watchlists_user_id'), ['user_id'], unique=False)

    op.create_table('portfolios',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('initial_capital', sa.Float(), nullable=False),
    sa.Column('current_capital', sa.Float(), nullable=False),
    sa.Column('available_cash', sa.Float(), nullable=False),
    sa.Column('max_position_size', sa.Float(), nullable=True),
    sa.Column('max_daily_loss', sa.Float(), nullable=True),
    sa.Column('max_total_risk', sa.Float(), nullable=True),
    sa.Column('total_return', sa.Float(), nullable=True),
    sa.Column('total_return_percent', sa.Float(), nullable=True),
    sa.Column('day_return', sa.Float(), nullable=True),
    sa.Column('day_return_percent', sa.Float(), nullable=True),
    sa.Column('sharpe_ratio', sa.Float(), nullable=True),
    sa.Column('max_drawdown', sa.Float(), nullable=True),
    sa.Column('volatility', sa.Float(), nullable=True),
    sa.Column('beta', sa.Float(), nullable=True),
    sa.Column('total_trades', sa.Integer(), nullable=True),
    sa.Column('winning_trades', sa.Integer(), nullable=True),
    sa.Column('losing_trades', sa.Integer(), nullable=True),
    sa.Column('win_rate', sa.Float(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_paper_trading', sa.Boolean(), nullable=True),
    sa.Column('auto_rebalance', sa.Boolean(), nullable=True),
    sa.Column('rebalance_frequency', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_rebalanced', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_portfolios_user_id_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_portfolios'))
    )
    with op.batch_alter_table('portfolios', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_portfolios_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_portfolios_user_id'), ['user_id'], unique=False)

    op.create_table('strategies',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('portfolio_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('screening_criteria', sa.JSON(), nullable=False),
    sa.Column('entry_rules', sa.JSON(), nullable=False),
    sa.Column('exit_rules', sa.JSON(), nullable=False),
    sa.Column('risk_rules', sa.JSON(), nullable=True),
    sa.Column('position_size_method', sa.String(length=20), nullable=True),
    sa.Column('position_size_value', sa.Float(), nullable=True),
    sa.Column('max_positions', sa.Integer(), nullable=True),
    sa.Column('order_type', sa.String(length=20), nullable=True),
    sa.Column('execution_delay', sa.Integer(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'PAUSED', 'STOPPED', 'BACKTESTING', name='strategystatus'), nullable=True),
    sa.Column('is_paper_trading', sa.Boolean(), nullable=True),
    sa.Column('total_trades', sa.Integer(), nullable=True),
    sa.Column('winning_trades', sa.Integer(), nullable=True),
    sa.Column('total_return', sa.Float(), nullable=True),
    sa.Column('max_drawdown', sa.Float(), nullable=True),
    sa.Column('last_scan_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('next_scan_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('scan_interval', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['portfolio_id'], ['portfolios.id'], name=op.f('fk_strategies_portfolio_id_portfolios')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_strategies_user_id_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_strategies'))
    )
    with op.batch_alter_table('strategies', schema=None) as batch_op:
        batch_op.create_index('idx_strategy_next_scan', ['next_scan_time'], unique=False)
        batch_op.create_index('idx_strategy_status', ['status'], unique=False)
        batch_op.create_index(batch_op.f('ix_strategies_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_strategies_portfolio_id'), ['portfolio_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_strategies_user_id'), ['user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('strategies', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_strategies_user_id'))
        batch_op.drop_index(batch_op.f('ix_strategies_portfolio_id'))
        batch_op.drop_index(batch_op.f('ix_strategies_id'))
        batch_op.drop_index('idx_strategy_status')
        batch_op.drop_index('idx_strategy_next_scan')

    op.drop_table('strategies')
    with op.batch_alter_table('portfolios', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_portfolios_user_id'))
        batch_op.drop_index(batch_op.f('ix_portfolios_id'))

    op.drop_table('portfolios')
    with op.batch_alter_table('watchlists', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_watchlists_user_id'))
        batch_op.drop_index(batch_op.f('ix_watchlists_id'))

    op.drop_table('watchlists')
    with op.batch_alter_table('watchlist_items', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_watchlist_items_watchlist_id'))
        batch_op.drop_index(batch_op.f('ix_watchlist_items_stock_id'))
        batch_op.drop_index(batch_op.f('ix_watchlist_items_id'))

    op.drop_table('watchlist_items')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_username'))
        batch_op.drop_index(batch_op.f('ix_users_id'))
        batch_op.drop_index(batch_op.f('ix_users_email'))
        batch_op.drop_index(batch_op.f('ix_users_api_key_hash'))

    op.drop_table('users')
    with op.batch_alter_table('user_sessions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_sessions_user_id'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_session_token'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_refresh_token'))
        batch_op.drop_index(batch_op.f('ix_user_sessions_id'))

    op.drop_table('user_sessions')
    with op.batch_alter_table('trades', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_trades_user_id'))
        batch_op.drop_index(batch_op.f('ix_trades_strategy_id'))
        batch_op.drop_index(batch_op.f('ix_trades_stock_id'))
        batch_op.drop_index(batch_op.f('ix_trades_portfolio_id'))
        batch_op.drop_index(batch_op.f('ix_trades_id'))
        batch_op.drop_index(batch_op.f('ix_trades_broker_order_id'))
        batch_op.drop_index(batch_op.f('ix_trades_broker_account_id'))
        batch_op.drop_index('idx_trade_user_date')
        batch_op.drop_index('idx_trade_status')
        batch_op.drop_index('idx_trade_broker_order')

    op.drop_table('trades')
    with op.batch_alter_table('trade_history', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_trade_history_user_id'))
        batch_op.drop_index(batch_op.f('ix_trade_history_strategy_id'))
        batch_op.drop_index(batch_op.f('ix_trade_history_stock_id'))
        batch_op.drop_index(batch_op.f('ix_trade_history_id'))
        batch_op.drop_index('idx_trade_history_user_date')
        batch_op.drop_index('idx_trade_history_performance')

    op.drop_table('trade_history')
    with op.batch_alter_table('stocks', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_stocks_symbol'))
        batch_op.drop_index(batch_op.f('ix_stocks_id'))
        batch_op.drop_index('idx_stock_volume')
        batch_op.drop_index('idx_stock_sector_industry')
        batch_op.drop_index('idx_stock_price')
        batch_op.drop_index('idx_stock_market_cap')

    op.drop_table('stocks')
    with op.batch_alter_table('screening_results', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_screening_results_trade_id'))
        batch_op.drop_index(batch_op.f('ix_screening_results_strategy_id'))
        batch_op.drop_index(batch_op.f('ix_screening_results_stock_id'))
        batch_op.drop_index(batch_op.f('ix_screening_results_id'))
        batch_op.drop_index('idx_screening_result_strategy_timestamp')
        batch_op.drop_index('idx_screening_result_signal')
        batch_op.drop_index('idx_screening_result_score')

    op.drop_table('screening_results')
    with op.batch_alter_table('positions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_positions_user_id'))
        batch_op.drop_index(batch_op.f('ix_positions_stock_id'))
        batch_op.drop_index(batch_op.f('ix_positions_portfolio_id'))
        batch_op.drop_index(batch_op.f('ix_positions_id'))
        batch_op.drop_index(batch_op.f('ix_positions_broker_account_id'))
        batch_op.drop_index('idx_position_user_stock')
        batch_op.drop_index('idx_position_broker_account')

    op.drop_table('positions')
    with op.batch_alter_table('market_data', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_market_data_symbol'))
        batch_op.drop_index(batch_op.f('ix_market_data_id'))
        batch_op.drop_index('idx_market_data_timeframe')
        batch_op.drop_index('idx_market_data_symbol_timestamp')

    op.drop_table('market_data')
    with op.batch_alter_table('broker_accounts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_broker_accounts_user_id'))
        batch_op.drop_index(batch_op.f('ix_broker_accounts_id'))

    op.drop_table('broker_accounts')
    # ### end Alembic commands ###
