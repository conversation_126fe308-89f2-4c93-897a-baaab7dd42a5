"""
Stock Screening API endpoints
Provides comprehensive stock screening functionality with rate limiting and validation
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query, BackgroundTasks
from fastapi.security import HTTPBearer
from pydantic import BaseModel, Field, validator
from sqlalchemy import select, and_, or_, desc
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import asyncio
from enum import Enum

from app.core.database import get_db, CacheManager
from app.core.security import get_current_active_user, require_subscription_tier
from app.models.user import User
from app.models.stock import Stock, ScreeningResult
from app.models.portfolio import Strategy
from app.services.screening_engine import ScreeningEngine
from app.services.data_provider import DataProvider
from app.utils.rate_limiter import RateLimiter
from app.utils.validators import validate_screening_criteria
from app.core.config import settings

router = APIRouter()
security = HTTPBearer()

# Rate limiter for screening operations
screening_rate_limiter = RateLimiter(
    max_requests=100,  # Free tier: 100 requests per hour
    time_window=3600,
    premium_max_requests=1000,  # Premium tier: 1000 requests per hour
    enterprise_max_requests=10000  # Enterprise tier: 10000 requests per hour
)

# Enums for screening parameters
class SortOrder(str, Enum):
    ASC = "asc"
    DESC = "desc"

class ScreeningStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

# Pydantic models for request/response
class ScreeningCriteria(BaseModel):
    """Individual screening criterion"""
    field: str = Field(..., description="Field to screen (e.g., 'price', 'volume', 'rsi')")
    operator: str = Field(..., description="Comparison operator (>, <, >=, <=, ==, !=)")
    value: float = Field(..., description="Value to compare against")
    weight: float = Field(default=1.0, ge=0.1, le=10.0, description="Weight of this criterion")

    @validator('operator')
    def validate_operator(cls, v):
        allowed_operators = ['>', '<', '>=', '<=', '==', '!=', 'between', 'in']
        if v not in allowed_operators:
            raise ValueError(f'Operator must be one of: {allowed_operators}')
        return v

class StrategyCreate(BaseModel):
    """Create new screening strategy"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    screening_criteria: List[ScreeningCriteria] = Field(..., min_items=1, max_items=20)
    universe: List[str] = Field(default=["SP500"], description="Stock universe to screen")
    min_score: float = Field(default=70.0, ge=0.0, le=100.0)
    max_results: int = Field(default=50, ge=1, le=500)
    is_active: bool = Field(default=True)
    schedule_enabled: bool = Field(default=False)
    schedule_frequency: Optional[str] = Field(default=None)

    @validator('screening_criteria')
    def validate_criteria(cls, v):
        return validate_screening_criteria(v)

class StrategyUpdate(BaseModel):
    """Update existing screening strategy"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    screening_criteria: Optional[List[ScreeningCriteria]] = Field(None, min_items=1, max_items=20)
    universe: Optional[List[str]] = None
    min_score: Optional[float] = Field(None, ge=0.0, le=100.0)
    max_results: Optional[int] = Field(None, ge=1, le=500)
    is_active: Optional[bool] = None
    schedule_enabled: Optional[bool] = None
    schedule_frequency: Optional[str] = None

class StrategyResponse(BaseModel):
    """Strategy response model"""
    id: int
    name: str
    description: Optional[str]
    screening_criteria: List[Dict[str, Any]]
    universe: List[str]
    min_score: float
    max_results: int
    is_active: bool
    schedule_enabled: bool
    schedule_frequency: Optional[str]
    created_at: str
    updated_at: Optional[str]
    last_run: Optional[str]
    total_runs: int

class ScreeningRequest(BaseModel):
    """Request to run screening"""
    strategy_id: int
    symbols: Optional[List[str]] = Field(None, max_items=1000)
    force_refresh: bool = Field(default=False)
    save_results: bool = Field(default=True)

class ScreeningResultResponse(BaseModel):
    """Screening result response"""
    id: int
    strategy_id: int
    stock_symbol: str
    stock_name: str
    scan_timestamp: str
    score: float
    signal_strength: str
    entry_price: Optional[float]
    target_price: Optional[float]
    stop_loss_price: Optional[float]
    criteria_met: Dict[str, Any]
    market_data: Dict[str, Any]

class ScreeningJobResponse(BaseModel):
    """Screening job status response"""
    job_id: str
    status: ScreeningStatus
    strategy_id: int
    progress: float
    total_symbols: int
    processed_symbols: int
    results_count: int
    started_at: str
    completed_at: Optional[str]
    error_message: Optional[str]

# Global screening jobs tracker
screening_jobs: Dict[str, Dict[str, Any]] = {}

@router.get("/strategies", response_model=List[StrategyResponse])
async def get_strategies(
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    active_only: bool = Query(True)
):
    """Get user's screening strategies"""
    async with get_db() as db:
        query = select(Strategy).where(Strategy.user_id == current_user.id)
        
        if active_only:
            query = query.where(Strategy.is_active == True)
        
        query = query.order_by(desc(Strategy.created_at)).offset(skip).limit(limit)
        
        result = await db.execute(query)
        strategies = result.scalars().all()
        
        return [
            StrategyResponse(
                id=strategy.id,
                name=strategy.name,
                description=strategy.description,
                screening_criteria=strategy.screening_criteria,
                universe=strategy.universe,
                min_score=strategy.min_score,
                max_results=strategy.max_results,
                is_active=strategy.is_active,
                schedule_enabled=strategy.schedule_enabled,
                schedule_frequency=strategy.schedule_frequency,
                created_at=strategy.created_at.isoformat(),
                updated_at=strategy.updated_at.isoformat() if strategy.updated_at else None,
                last_run=strategy.last_run.isoformat() if strategy.last_run else None,
                total_runs=strategy.total_runs
            )
            for strategy in strategies
        ]

@router.post("/strategies", response_model=StrategyResponse, status_code=status.HTTP_201_CREATED)
async def create_strategy(
    strategy_data: StrategyCreate,
    current_user: User = Depends(get_current_active_user)
):
    """Create new screening strategy"""
    # Check subscription limits
    if current_user.subscription_tier == "free":
        async with get_db() as db:
            result = await db.execute(
                select(Strategy).where(Strategy.user_id == current_user.id)
            )
            existing_strategies = result.scalars().all()
            if len(existing_strategies) >= 3:  # Free tier limit
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Free tier limited to 3 strategies. Upgrade to create more."
                )
    
    async with get_db() as db:
        # Check for duplicate strategy name
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.user_id == current_user.id,
                    Strategy.name == strategy_data.name
                )
            )
        )
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Strategy with this name already exists"
            )
        
        # Create new strategy
        new_strategy = Strategy(
            user_id=current_user.id,
            name=strategy_data.name,
            description=strategy_data.description,
            screening_criteria=[criterion.dict() for criterion in strategy_data.screening_criteria],
            universe=strategy_data.universe,
            min_score=strategy_data.min_score,
            max_results=strategy_data.max_results,
            is_active=strategy_data.is_active,
            schedule_enabled=strategy_data.schedule_enabled,
            schedule_frequency=strategy_data.schedule_frequency
        )
        
        db.add(new_strategy)
        await db.commit()
        await db.refresh(new_strategy)
        
        return StrategyResponse(
            id=new_strategy.id,
            name=new_strategy.name,
            description=new_strategy.description,
            screening_criteria=new_strategy.screening_criteria,
            universe=new_strategy.universe,
            min_score=new_strategy.min_score,
            max_results=new_strategy.max_results,
            is_active=new_strategy.is_active,
            schedule_enabled=new_strategy.schedule_enabled,
            schedule_frequency=new_strategy.schedule_frequency,
            created_at=new_strategy.created_at.isoformat(),
            updated_at=new_strategy.updated_at.isoformat() if new_strategy.updated_at else None,
            last_run=new_strategy.last_run.isoformat() if new_strategy.last_run else None,
            total_runs=new_strategy.total_runs
        )

@router.get("/strategies/{strategy_id}", response_model=StrategyResponse)
async def get_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Get specific screening strategy"""
    async with get_db() as db:
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = result.scalar_one_or_none()

        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )

        return StrategyResponse(
            id=strategy.id,
            name=strategy.name,
            description=strategy.description,
            screening_criteria=strategy.screening_criteria,
            universe=strategy.universe,
            min_score=strategy.min_score,
            max_results=strategy.max_results,
            is_active=strategy.is_active,
            schedule_enabled=strategy.schedule_enabled,
            schedule_frequency=strategy.schedule_frequency,
            created_at=strategy.created_at.isoformat(),
            updated_at=strategy.updated_at.isoformat() if strategy.updated_at else None,
            last_run=strategy.last_run.isoformat() if strategy.last_run else None,
            total_runs=strategy.total_runs
        )

@router.put("/strategies/{strategy_id}", response_model=StrategyResponse)
async def update_strategy(
    strategy_id: int,
    strategy_data: StrategyUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """Update existing screening strategy"""
    async with get_db() as db:
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = result.scalar_one_or_none()

        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )

        # Update fields if provided
        update_data = strategy_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if field == "screening_criteria" and value:
                setattr(strategy, field, [criterion.dict() for criterion in value])
            else:
                setattr(strategy, field, value)

        strategy.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(strategy)

        return StrategyResponse(
            id=strategy.id,
            name=strategy.name,
            description=strategy.description,
            screening_criteria=strategy.screening_criteria,
            universe=strategy.universe,
            min_score=strategy.min_score,
            max_results=strategy.max_results,
            is_active=strategy.is_active,
            schedule_enabled=strategy.schedule_enabled,
            schedule_frequency=strategy.schedule_frequency,
            created_at=strategy.created_at.isoformat(),
            updated_at=strategy.updated_at.isoformat() if strategy.updated_at else None,
            last_run=strategy.last_run.isoformat() if strategy.last_run else None,
            total_runs=strategy.total_runs
        )

@router.delete("/strategies/{strategy_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_strategy(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Delete screening strategy"""
    async with get_db() as db:
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = result.scalar_one_or_none()

        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )

        await db.delete(strategy)
        await db.commit()

@router.post("/strategies/{strategy_id}/run", response_model=ScreeningJobResponse)
async def run_screening(
    strategy_id: int,
    background_tasks: BackgroundTasks,
    request_data: Optional[ScreeningRequest] = None,
    current_user: User = Depends(get_current_active_user)
):
    """Run screening for a strategy"""
    # Apply rate limiting
    await screening_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier
    )

    async with get_db() as db:
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = result.scalar_one_or_none()

        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )

        if not strategy.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Strategy is not active"
            )

        # Generate job ID
        import uuid
        job_id = str(uuid.uuid4())

        # Initialize job tracking
        screening_jobs[job_id] = {
            "job_id": job_id,
            "status": ScreeningStatus.PENDING,
            "strategy_id": strategy_id,
            "user_id": current_user.id,
            "progress": 0.0,
            "total_symbols": 0,
            "processed_symbols": 0,
            "results_count": 0,
            "started_at": datetime.utcnow(),
            "completed_at": None,
            "error_message": None,
            "symbols": request_data.symbols if request_data else None,
            "force_refresh": request_data.force_refresh if request_data else False,
            "save_results": request_data.save_results if request_data else True
        }

        # Start background screening task
        background_tasks.add_task(
            _run_screening_background,
            job_id,
            strategy,
            current_user
        )

        return ScreeningJobResponse(
            job_id=job_id,
            status=ScreeningStatus.PENDING,
            strategy_id=strategy_id,
            progress=0.0,
            total_symbols=0,
            processed_symbols=0,
            results_count=0,
            started_at=datetime.utcnow().isoformat(),
            completed_at=None,
            error_message=None
        )

async def _run_screening_background(job_id: str, strategy: Strategy, user: User):
    """Background task to run screening"""
    try:
        job_data = screening_jobs[job_id]
        job_data["status"] = ScreeningStatus.RUNNING

        # Get data provider and screening engine
        from app.main import data_provider, screening_engine

        if not data_provider or not screening_engine:
            raise Exception("Data provider or screening engine not available")

        # Get symbols to screen
        symbols = job_data["symbols"]
        if not symbols:
            # Use strategy universe
            if "SP500" in strategy.universe:
                symbols = await data_provider.get_sp500_symbols()
            elif "NASDAQ100" in strategy.universe:
                symbols = await data_provider.get_nasdaq100_symbols()
            else:
                symbols = strategy.universe

        job_data["total_symbols"] = len(symbols)

        # Run screening
        results = []
        for i, symbol in enumerate(symbols):
            try:
                # Get stock data
                async with get_db() as db:
                    result = await db.execute(
                        select(Stock).where(Stock.symbol == symbol)
                    )
                    stock = result.scalar_one_or_none()

                    if not stock:
                        # Create stock record if not exists
                        stock = Stock(symbol=symbol, name=symbol)
                        db.add(stock)
                        await db.commit()
                        await db.refresh(stock)

                # Run screening for this stock
                screening_result = await screening_engine._screen_stock(strategy, stock)

                if screening_result and job_data["save_results"]:
                    async with get_db() as db:
                        db.add(screening_result)
                        await db.commit()
                        results.append(screening_result)

                # Update progress
                job_data["processed_symbols"] = i + 1
                job_data["progress"] = (i + 1) / len(symbols) * 100
                job_data["results_count"] = len(results)

            except Exception as e:
                # Log error but continue processing
                print(f"Error screening {symbol}: {e}")
                continue

        # Update strategy stats
        async with get_db() as db:
            strategy.last_run = datetime.utcnow()
            strategy.total_runs += 1
            await db.commit()

        # Mark job as completed
        job_data["status"] = ScreeningStatus.COMPLETED
        job_data["completed_at"] = datetime.utcnow()
        job_data["progress"] = 100.0

    except Exception as e:
        # Mark job as failed
        job_data["status"] = ScreeningStatus.FAILED
        job_data["error_message"] = str(e)
        job_data["completed_at"] = datetime.utcnow()

@router.get("/jobs/{job_id}", response_model=ScreeningJobResponse)
async def get_screening_job_status(
    job_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get screening job status"""
    if job_id not in screening_jobs:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Screening job not found"
        )

    job_data = screening_jobs[job_id]

    # Check if user owns this job
    if job_data["user_id"] != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this screening job"
        )

    return ScreeningJobResponse(
        job_id=job_data["job_id"],
        status=job_data["status"],
        strategy_id=job_data["strategy_id"],
        progress=job_data["progress"],
        total_symbols=job_data["total_symbols"],
        processed_symbols=job_data["processed_symbols"],
        results_count=job_data["results_count"],
        started_at=job_data["started_at"].isoformat(),
        completed_at=job_data["completed_at"].isoformat() if job_data["completed_at"] else None,
        error_message=job_data["error_message"]
    )

@router.get("/strategies/{strategy_id}/results", response_model=List[ScreeningResultResponse])
async def get_screening_results(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    min_score: Optional[float] = Query(None, ge=0.0, le=100.0),
    sort_by: str = Query("score", regex="^(score|scan_timestamp|symbol)$"),
    sort_order: SortOrder = Query(SortOrder.DESC)
):
    """Get screening results for a strategy"""
    async with get_db() as db:
        # Verify strategy ownership
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = result.scalar_one_or_none()

        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )

        # Build query
        query = select(ScreeningResult).options(
            selectinload(ScreeningResult.stock)
        ).where(ScreeningResult.strategy_id == strategy_id)

        if min_score is not None:
            query = query.where(ScreeningResult.score >= min_score)

        # Apply sorting
        if sort_by == "score":
            order_col = ScreeningResult.score
        elif sort_by == "scan_timestamp":
            order_col = ScreeningResult.scan_timestamp
        else:  # symbol
            order_col = ScreeningResult.stock.symbol

        if sort_order == SortOrder.DESC:
            query = query.order_by(desc(order_col))
        else:
            query = query.order_by(order_col)

        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        screening_results = result.scalars().all()

        return [
            ScreeningResultResponse(
                id=result.id,
                strategy_id=result.strategy_id,
                stock_symbol=result.stock.symbol,
                stock_name=result.stock.name or result.stock.symbol,
                scan_timestamp=result.scan_timestamp.isoformat(),
                score=result.score,
                signal_strength=result.signal_strength,
                entry_price=result.entry_price,
                target_price=result.target_price,
                stop_loss_price=result.stop_loss_price,
                criteria_met=result.criteria_met,
                market_data={}  # Would be populated from market data service
            )
            for result in screening_results
        ]

@router.delete("/strategies/{strategy_id}/results", status_code=status.HTTP_204_NO_CONTENT)
async def clear_screening_results(
    strategy_id: int,
    current_user: User = Depends(get_current_active_user),
    older_than_days: Optional[int] = Query(None, ge=1, le=365)
):
    """Clear screening results for a strategy"""
    async with get_db() as db:
        # Verify strategy ownership
        result = await db.execute(
            select(Strategy).where(
                and_(
                    Strategy.id == strategy_id,
                    Strategy.user_id == current_user.id
                )
            )
        )
        strategy = result.scalar_one_or_none()

        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Strategy not found"
            )

        # Build delete query
        query = select(ScreeningResult).where(ScreeningResult.strategy_id == strategy_id)

        if older_than_days:
            cutoff_date = datetime.utcnow() - timedelta(days=older_than_days)
            query = query.where(ScreeningResult.scan_timestamp < cutoff_date)

        result = await db.execute(query)
        results_to_delete = result.scalars().all()

        for result in results_to_delete:
            await db.delete(result)

        await db.commit()

@router.get("/market-data/{symbol}")
async def get_market_data(
    symbol: str,
    current_user: User = Depends(get_current_active_user),
    include_indicators: bool = Query(False),
    include_fundamentals: bool = Query(False)
):
    """Get current market data for a symbol"""
    # Apply rate limiting
    await screening_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier
    )

    try:
        from app.main import data_provider

        if not data_provider:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Data provider not available"
            )

        # Get basic quote data
        quote_data = await data_provider.get_stock_quote(symbol.upper())
        if not quote_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Market data not found for symbol {symbol}"
            )

        response_data = {
            "symbol": symbol.upper(),
            "quote": quote_data,
            "timestamp": datetime.utcnow().isoformat()
        }

        # Add technical indicators if requested
        if include_indicators:
            indicators = await data_provider.get_technical_indicators(
                symbol.upper(),
                ["rsi", "macd", "bollinger_bands", "sma_20", "sma_50", "sma_200"]
            )
            response_data["indicators"] = indicators

        # Add fundamental data if requested (premium feature)
        if include_fundamentals:
            if current_user.subscription_tier == "free":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Fundamental data requires premium subscription"
                )

            fundamentals = await data_provider.get_fundamental_data(symbol.upper())
            response_data["fundamentals"] = fundamentals

        return response_data

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching market data: {str(e)}"
        )

@router.get("/universes")
async def get_available_universes(
    current_user: User = Depends(get_current_active_user)
):
    """Get available stock universes for screening"""
    universes = {
        "SP500": {
            "name": "S&P 500",
            "description": "Large-cap US stocks",
            "symbol_count": 500,
            "subscription_required": "free"
        },
        "NASDAQ100": {
            "name": "NASDAQ 100",
            "description": "Top 100 NASDAQ stocks",
            "symbol_count": 100,
            "subscription_required": "free"
        },
        "RUSSELL2000": {
            "name": "Russell 2000",
            "description": "Small-cap US stocks",
            "symbol_count": 2000,
            "subscription_required": "premium"
        },
        "ALL_US": {
            "name": "All US Stocks",
            "description": "All US listed stocks",
            "symbol_count": 8000,
            "subscription_required": "enterprise"
        }
    }

    # Filter based on subscription tier
    available_universes = {}
    for key, universe in universes.items():
        if (universe["subscription_required"] == "free" or
            (universe["subscription_required"] == "premium" and current_user.subscription_tier in ["premium", "enterprise"]) or
            (universe["subscription_required"] == "enterprise" and current_user.subscription_tier == "enterprise")):
            available_universes[key] = universe

    return available_universes

@router.get("/criteria-fields")
async def get_screening_criteria_fields(
    current_user: User = Depends(get_current_active_user)
):
    """Get available fields for screening criteria"""
    basic_fields = {
        "price": {"name": "Price", "type": "number", "description": "Current stock price"},
        "volume": {"name": "Volume", "type": "number", "description": "Trading volume"},
        "market_cap": {"name": "Market Cap", "type": "number", "description": "Market capitalization"},
        "rsi": {"name": "RSI", "type": "number", "description": "Relative Strength Index (0-100)"},
        "sma_20": {"name": "SMA 20", "type": "number", "description": "20-day Simple Moving Average"},
        "sma_50": {"name": "SMA 50", "type": "number", "description": "50-day Simple Moving Average"},
        "sma_200": {"name": "SMA 200", "type": "number", "description": "200-day Simple Moving Average"}
    }

    premium_fields = {
        "pe_ratio": {"name": "P/E Ratio", "type": "number", "description": "Price-to-Earnings ratio"},
        "pb_ratio": {"name": "P/B Ratio", "type": "number", "description": "Price-to-Book ratio"},
        "debt_to_equity": {"name": "Debt/Equity", "type": "number", "description": "Debt-to-Equity ratio"},
        "roe": {"name": "ROE", "type": "number", "description": "Return on Equity"},
        "revenue_growth": {"name": "Revenue Growth", "type": "number", "description": "Revenue growth rate"},
        "eps_growth": {"name": "EPS Growth", "type": "number", "description": "Earnings per share growth"}
    }

    enterprise_fields = {
        "beta": {"name": "Beta", "type": "number", "description": "Stock beta (volatility measure)"},
        "dividend_yield": {"name": "Dividend Yield", "type": "number", "description": "Dividend yield percentage"},
        "free_cash_flow": {"name": "Free Cash Flow", "type": "number", "description": "Free cash flow"},
        "analyst_rating": {"name": "Analyst Rating", "type": "number", "description": "Average analyst rating"}
    }

    # Return fields based on subscription tier
    available_fields = basic_fields.copy()

    if current_user.subscription_tier in ["premium", "enterprise"]:
        available_fields.update(premium_fields)

    if current_user.subscription_tier == "enterprise":
        available_fields.update(enterprise_fields)

    return available_fields
