"""
Stock screening engine for automated strategy execution
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy import select, and_, or_

from app.core.config import settings
from app.core.database import get_db
from app.models.stock import Stock
from app.models.portfolio import Strategy, StrategyStatus
from app.models.stock import ScreeningResult
from app.services.data_provider import DataProvider
from app.utils.indicators import TechnicalIndicators

logger = logging.getLogger(__name__)

class ScreeningEngine:
    """Main screening engine for automated stock screening"""
    
    def __init__(self, data_provider: DataProvider):
        self.data_provider = data_provider
        self.is_running = False
        self.active_strategies: List[Strategy] = []
        self.screening_tasks = {}
    
    async def start(self):
        """Start the screening engine"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # Start background screening task
        asyncio.create_task(self._screening_loop())
        
        logger.info("Screening engine started")
    
    async def stop(self):
        """Stop the screening engine"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel all screening tasks
        for task in self.screening_tasks.values():
            task.cancel()
        
        logger.info("Screening engine stopped")
    
    async def _screening_loop(self):
        """Main screening loop"""
        while self.is_running:
            try:
                await self._load_active_strategies()
                await self._process_strategies()
                await asyncio.sleep(settings.SCREENING_INTERVAL)
            except Exception as e:
                logger.error(f"Error in screening loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _load_active_strategies(self):
        """Load active strategies from database"""
        try:
            async with get_db() as db:
                result = await db.execute(
                    select(Strategy).where(
                        and_(
                            Strategy.status == StrategyStatus.ACTIVE,
                            or_(
                                Strategy.next_scan_time.is_(None),
                                Strategy.next_scan_time <= datetime.utcnow()
                            )
                        )
                    )
                )
                self.active_strategies = result.scalars().all()
        except Exception as e:
            logger.error(f"Error loading active strategies: {e}")
            self.active_strategies = []
    
    async def _process_strategies(self):
        """Process all active strategies"""
        for strategy in self.active_strategies:
            try:
                await self._process_strategy(strategy)
            except Exception as e:
                logger.error(f"Error processing strategy {strategy.id}: {e}")
    
    async def _process_strategy(self, strategy: Strategy):
        """Process a single strategy"""
        logger.info(f"Processing strategy: {strategy.name} (ID: {strategy.id})")
        
        # Get stocks to screen
        stocks = await self._get_stocks_to_screen(strategy)
        
        # Screen each stock
        screening_results = []
        for stock in stocks:
            try:
                result = await self._screen_stock(strategy, stock)
                if result:
                    screening_results.append(result)
            except Exception as e:
                logger.error(f"Error screening stock {stock.symbol}: {e}")
        
        # Save screening results
        await self._save_screening_results(screening_results)
        
        # Update strategy scan time
        await self._update_strategy_scan_time(strategy)
        
        logger.info(f"Strategy {strategy.name} processed. Found {len(screening_results)} matches.")
    
    async def _get_stocks_to_screen(self, strategy: Strategy) -> List[Stock]:
        """Get list of stocks to screen for a strategy"""
        try:
            async with get_db() as db:
                # For now, get all active and tradeable stocks
                # In production, this could be filtered based on strategy criteria
                result = await db.execute(
                    select(Stock).where(
                        and_(
                            Stock.is_active == True,
                            Stock.is_tradeable == True
                        )
                    ).limit(1000)  # Limit to prevent overload
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting stocks to screen: {e}")
            return []
    
    async def _screen_stock(self, strategy: Strategy, stock: Stock) -> Optional[ScreeningResult]:
        """Screen a single stock against strategy criteria"""
        try:
            # Get current market data
            quote_data = await self.data_provider.get_stock_quote(stock.symbol)
            if not quote_data:
                return None
            
            # Get technical indicators
            indicators = await self.data_provider.get_technical_indicators(
                stock.symbol,
                ["rsi", "macd", "bollinger_bands", "sma_20", "sma_50", "sma_200"]
            )
            
            # Get fundamental data
            fundamentals = await self.data_provider.get_fundamental_data(stock.symbol)
            
            # Evaluate screening criteria
            criteria_results = await self._evaluate_criteria(
                strategy.screening_criteria,
                stock,
                quote_data,
                indicators,
                fundamentals
            )
            
            # Calculate overall score
            score = self._calculate_screening_score(criteria_results)
            
            # Check if criteria are met
            if score >= 70:  # Threshold for signal generation
                signal_strength = self._determine_signal_strength(score)
                
                # Calculate entry, target, and stop prices
                entry_price = quote_data.get('price')
                target_price, stop_loss_price = self._calculate_trade_levels(
                    entry_price, indicators, strategy
                )
                
                return ScreeningResult(
                    strategy_id=strategy.id,
                    stock_id=stock.id,
                    scan_timestamp=datetime.utcnow(),
                    criteria_met=criteria_results,
                    score=score,
                    signal_strength=signal_strength,
                    entry_price=entry_price,
                    target_price=target_price,
                    stop_loss_price=stop_loss_price,
                    action_taken="none"
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error screening stock {stock.symbol}: {e}")
            return None
    
    async def _evaluate_criteria(
        self,
        criteria: Dict[str, Any],
        stock: Stock,
        quote_data: Dict[str, Any],
        indicators: Dict[str, Any],
        fundamentals: Optional[Dict[str, Any]]
    ) -> Dict[str, bool]:
        """Evaluate screening criteria against stock data"""
        results = {}
        
        try:
            # Technical criteria
            if "technical" in criteria:
                tech_criteria = criteria["technical"]
                
                # RSI criteria
                if "rsi" in tech_criteria and "rsi" in indicators:
                    rsi_value = indicators["rsi"]
                    rsi_criteria = tech_criteria["rsi"]
                    
                    if "min" in rsi_criteria:
                        results["rsi_min"] = rsi_value >= rsi_criteria["min"]
                    if "max" in rsi_criteria:
                        results["rsi_max"] = rsi_value <= rsi_criteria["max"]
                
                # Price vs Moving Average criteria
                current_price = quote_data.get("price", 0)
                
                if "above_sma_20" in tech_criteria and "sma_20" in indicators:
                    results["above_sma_20"] = current_price > indicators["sma_20"]
                
                if "above_sma_50" in tech_criteria and "sma_50" in indicators:
                    results["above_sma_50"] = current_price > indicators["sma_50"]
                
                if "above_sma_200" in tech_criteria and "sma_200" in indicators:
                    results["above_sma_200"] = current_price > indicators["sma_200"]
                
                # Volume criteria
                if "min_volume" in tech_criteria:
                    volume = quote_data.get("volume", 0)
                    results["min_volume"] = volume >= tech_criteria["min_volume"]
            
            # Fundamental criteria
            if "fundamental" in criteria and fundamentals:
                fund_criteria = criteria["fundamental"]
                
                # P/E ratio criteria
                if "pe_ratio" in fund_criteria and fundamentals.get("pe_ratio"):
                    pe_ratio = fundamentals["pe_ratio"]
                    pe_criteria = fund_criteria["pe_ratio"]
                    
                    if "min" in pe_criteria:
                        results["pe_min"] = pe_ratio >= pe_criteria["min"]
                    if "max" in pe_criteria:
                        results["pe_max"] = pe_ratio <= pe_criteria["max"]
                
                # Market cap criteria
                if "market_cap" in fund_criteria and fundamentals.get("market_cap"):
                    market_cap = fundamentals["market_cap"]
                    mc_criteria = fund_criteria["market_cap"]
                    
                    if "min" in mc_criteria:
                        results["market_cap_min"] = market_cap >= mc_criteria["min"]
                    if "max" in mc_criteria:
                        results["market_cap_max"] = market_cap <= mc_criteria["max"]
                
                # Sector criteria
                if "sectors" in fund_criteria and fundamentals.get("sector"):
                    allowed_sectors = fund_criteria["sectors"]
                    results["sector_allowed"] = fundamentals["sector"] in allowed_sectors
            
            # Price criteria
            if "price" in criteria:
                price_criteria = criteria["price"]
                current_price = quote_data.get("price", 0)
                
                if "min" in price_criteria:
                    results["price_min"] = current_price >= price_criteria["min"]
                if "max" in price_criteria:
                    results["price_max"] = current_price <= price_criteria["max"]
            
            return results
            
        except Exception as e:
            logger.error(f"Error evaluating criteria: {e}")
            return {}
    
    def _calculate_screening_score(self, criteria_results: Dict[str, bool]) -> float:
        """Calculate overall screening score based on criteria results"""
        if not criteria_results:
            return 0.0
        
        # Simple scoring: percentage of criteria met
        total_criteria = len(criteria_results)
        met_criteria = sum(1 for result in criteria_results.values() if result)
        
        return (met_criteria / total_criteria) * 100
    
    def _determine_signal_strength(self, score: float) -> str:
        """Determine signal strength based on score"""
        if score >= 90:
            return "strong"
        elif score >= 80:
            return "medium"
        else:
            return "weak"
    
    def _calculate_trade_levels(
        self,
        entry_price: float,
        indicators: Dict[str, Any],
        strategy: Strategy
    ) -> tuple[Optional[float], Optional[float]]:
        """Calculate target and stop loss prices"""
        try:
            # Simple calculation based on ATR or percentage
            # In production, this would be more sophisticated
            
            # Default to percentage-based levels
            target_multiplier = 1.02  # 2% target
            stop_multiplier = 0.98    # 2% stop loss
            
            # Adjust based on volatility if available
            if "bollinger_bands" in indicators:
                bb = indicators["bollinger_bands"]
                if bb.get("upper") and bb.get("lower") and bb.get("middle"):
                    # Use Bollinger Band width as volatility measure
                    bb_width = (bb["upper"] - bb["lower"]) / bb["middle"]
                    
                    # Adjust multipliers based on volatility
                    if bb_width > 0.1:  # High volatility
                        target_multiplier = 1.03
                        stop_multiplier = 0.97
                    elif bb_width < 0.05:  # Low volatility
                        target_multiplier = 1.015
                        stop_multiplier = 0.985
            
            target_price = entry_price * target_multiplier
            stop_loss_price = entry_price * stop_multiplier
            
            return target_price, stop_loss_price
            
        except Exception as e:
            logger.error(f"Error calculating trade levels: {e}")
            return None, None
    
    async def _save_screening_results(self, results: List[ScreeningResult]):
        """Save screening results to database"""
        if not results:
            return
        
        try:
            async with get_db() as db:
                for result in results:
                    db.add(result)
                await db.commit()
        except Exception as e:
            logger.error(f"Error saving screening results: {e}")
    
    async def _update_strategy_scan_time(self, strategy: Strategy):
        """Update strategy's next scan time"""
        try:
            next_scan = datetime.utcnow() + timedelta(seconds=strategy.scan_interval)
            
            async with get_db() as db:
                strategy.last_scan_time = datetime.utcnow()
                strategy.next_scan_time = next_scan
                await db.commit()
        except Exception as e:
            logger.error(f"Error updating strategy scan time: {e}")

    async def screen_stocks_for_strategy(
        self,
        strategy,
        symbols: Optional[List[str]] = None,
        force_refresh: bool = False
    ) -> List:
        """
        Public method to screen stocks for a strategy
        Used by the API endpoints
        """
        try:
            if symbols:
                # Screen specific symbols
                results = []
                async with get_db() as db:
                    for symbol in symbols:
                        # Get or create stock
                        result = await db.execute(
                            select(Stock).where(Stock.symbol == symbol.upper())
                        )
                        stock = result.scalar_one_or_none()

                        if not stock:
                            stock = Stock(symbol=symbol.upper(), name=symbol.upper())
                            db.add(stock)
                            await db.commit()
                            await db.refresh(stock)

                        # Screen the stock
                        screening_result = await self._screen_stock(strategy, stock)
                        if screening_result:
                            results.append(screening_result)

                return results
            else:
                # Use strategy's default screening
                await self._process_strategy(strategy)

                # Get recent results
                async with get_db() as db:
                    result = await db.execute(
                        select(ScreeningResult).where(
                            and_(
                                ScreeningResult.strategy_id == strategy.id,
                                ScreeningResult.scan_timestamp >= datetime.utcnow() - timedelta(hours=1)
                            )
                        ).order_by(ScreeningResult.score.desc()).limit(strategy.max_results or 50)
                    )
                    return result.scalars().all()

        except Exception as e:
            logger.error(f"Error screening stocks for strategy: {e}")
            return []
    
    async def run_manual_screen(
        self,
        strategy_id: int,
        symbols: Optional[List[str]] = None
    ) -> List[ScreeningResult]:
        """Run manual screening for a specific strategy"""
        try:
            async with get_db() as db:
                strategy = await db.get(Strategy, strategy_id)
                if not strategy:
                    raise ValueError(f"Strategy {strategy_id} not found")
                
                # Get stocks to screen
                if symbols:
                    # Screen specific symbols
                    result = await db.execute(
                        select(Stock).where(Stock.symbol.in_(symbols))
                    )
                    stocks = result.scalars().all()
                else:
                    # Screen all stocks
                    stocks = await self._get_stocks_to_screen(strategy)
                
                # Screen stocks
                screening_results = []
                for stock in stocks:
                    result = await self._screen_stock(strategy, stock)
                    if result:
                        screening_results.append(result)
                
                # Save results
                await self._save_screening_results(screening_results)
                
                return screening_results
                
        except Exception as e:
            logger.error(f"Error in manual screening: {e}")
            return []
    
    async def get_screening_history(
        self,
        strategy_id: int,
        limit: int = 100
    ) -> List[ScreeningResult]:
        """Get screening history for a strategy"""
        try:
            async with get_db() as db:
                result = await db.execute(
                    select(ScreeningResult)
                    .where(ScreeningResult.strategy_id == strategy_id)
                    .order_by(ScreeningResult.scan_timestamp.desc())
                    .limit(limit)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting screening history: {e}")
            return []
