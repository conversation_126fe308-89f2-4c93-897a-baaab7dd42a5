import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Strategy {
  id: number;
  name: string;
  status: string;
  screening_criteria: any;
}

interface ScreeningState {
  strategies: Strategy[];
  selectedStrategy: Strategy | null;
  screeningResults: any[];
  isLoading: boolean;
  error: string | null;
}

const initialState: ScreeningState = {
  strategies: [],
  selectedStrategy: null,
  screeningResults: [],
  isLoading: false,
  error: null,
};

const screeningSlice = createSlice({
  name: 'screening',
  initialState,
  reducers: {
    setStrategies: (state, action: PayloadAction<Strategy[]>) => {
      state.strategies = action.payload;
    },
    setSelectedStrategy: (state, action: PayloadAction<Strategy | null>) => {
      state.selectedStrategy = action.payload;
    },
    setScreeningResults: (state, action: PayloadAction<any[]>) => {
      state.screeningResults = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setStrategies, setSelectedStrategy, setScreeningResults, setLoading, setError } = screeningSlice.actions;
export default screeningSlice.reducer;
