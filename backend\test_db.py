#!/usr/bin/env python3
"""
Test database connection and create tables
"""

import asyncio
import os
import sys
from dotenv import load_dotenv

# Load environment variables from parent directory
load_dotenv('../.env')

# Add the app directory to the path
sys.path.append(os.path.dirname(__file__))

from app.core.database import init_db, close_db, Base
from app.core.config import settings

# Import all models to ensure they're registered
from app.models.user import User
from app.models.stock import Stock, ScreeningResult
from app.models.trade import Trade, Position, TradeHistory
from app.models.portfolio import Portfolio, Strategy

async def test_database():
    """Test database connection and create tables"""
    try:
        print(f"Testing database connection...")
        print(f"Database URL: {settings.DATABASE_URL}")
        
        # Initialize database
        await init_db()
        print("✅ Database initialized successfully!")
        
        # Test basic operations
        from app.core.database import get_db
        from sqlalchemy import text
        async with get_db() as db:
            # Test a simple query
            result = await db.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            print(f"✅ Database query test: {row}")
        
        print("✅ All database tests passed!")
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        raise
    finally:
        await close_db()

if __name__ == "__main__":
    asyncio.run(test_database())
