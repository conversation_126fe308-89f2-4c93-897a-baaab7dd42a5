version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: markethawk_postgres
    environment:
      POSTGRES_DB: markethawk
      POSTGRES_USER: markethawk
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - markethawk_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U markethawk -d markethawk"]
      interval: 30s
      timeout: 10s
      retries: 3

  # InfluxDB for Time Series Data
  influxdb:
    image: influxdb:2.7-alpine
    container_name: markethawk_influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: password123
      DOCKER_INFLUXDB_INIT_ORG: markethawk
      DOCKER_INFLUXDB_INIT_BUCKET: market_data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: markethawk-token-123456789
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    ports:
      - "8086:8086"
    networks:
      - markethawk_network
    healthcheck:
      test: ["CMD", "influx", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Caching and Sessions
  redis:
    image: redis:7-alpine
    container_name: markethawk_redis
    command: redis-server --appendonly yes --requirepass password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - markethawk_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: markethawk_backend
    environment:
      - DATABASE_URL=**********************************************/markethawk
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=markethawk-token-123456789
      - INFLUXDB_ORG=markethawk
      - INFLUXDB_BUCKET=market_data
      - REDIS_URL=redis://:password@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DEBUG=true
      - ENVIRONMENT=development
      - CORS_ORIGINS=["http://localhost:3000", "http://localhost:3001"]
      - PAPER_TRADING=true
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      influxdb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - markethawk_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: markethawk_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
      - REACT_APP_ENVIRONMENT=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - markethawk_network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: markethawk_nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - markethawk_network
    restart: unless-stopped

  # Prometheus for Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: markethawk_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - markethawk_network
    restart: unless-stopped

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: markethawk_grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - markethawk_network
    restart: unless-stopped

  # Celery Worker for Background Tasks
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: markethawk_celery_worker
    command: celery -A app.main worker --loglevel=info
    environment:
      - DATABASE_URL=**********************************************/markethawk
      - REDIS_URL=redis://:password@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-this-in-production
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
      - backend
    networks:
      - markethawk_network
    restart: unless-stopped

  # Celery Beat for Scheduled Tasks
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: markethawk_celery_beat
    command: celery -A app.main beat --loglevel=info
    environment:
      - DATABASE_URL=**********************************************/markethawk
      - REDIS_URL=redis://:password@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-this-in-production
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
      - backend
    networks:
      - markethawk_network
    restart: unless-stopped

volumes:
  postgres_data:
  influxdb_data:
  influxdb_config:
  redis_data:
  backend_logs:
  prometheus_data:
  grafana_data:

networks:
  markethawk_network:
    driver: bridge
