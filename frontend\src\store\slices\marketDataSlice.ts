import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Quote {
  symbol: string;
  price: number;
  change: number;
  change_percent: number;
  volume: number;
  timestamp: string;
}

interface MarketDataState {
  quotes: { [symbol: string]: Quote };
  watchlist: string[];
  marketStatus: any;
  isLoading: boolean;
  error: string | null;
}

const initialState: MarketDataState = {
  quotes: {},
  watchlist: [],
  marketStatus: null,
  isLoading: false,
  error: null,
};

const marketDataSlice = createSlice({
  name: 'marketData',
  initialState,
  reducers: {
    setQuote: (state, action: PayloadAction<Quote>) => {
      state.quotes[action.payload.symbol] = action.payload;
    },
    setQuotes: (state, action: PayloadAction<{ [symbol: string]: Quote }>) => {
      state.quotes = { ...state.quotes, ...action.payload };
    },
    setWatchlist: (state, action: PayloadAction<string[]>) => {
      state.watchlist = action.payload;
    },
    addToWatchlist: (state, action: PayloadAction<string>) => {
      if (!state.watchlist.includes(action.payload)) {
        state.watchlist.push(action.payload);
      }
    },
    removeFromWatchlist: (state, action: PayloadAction<string>) => {
      state.watchlist = state.watchlist.filter(symbol => symbol !== action.payload);
    },
    setMarketStatus: (state, action: PayloadAction<any>) => {
      state.marketStatus = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { 
  setQuote, 
  setQuotes, 
  setWatchlist, 
  addToWatchlist, 
  removeFromWatchlist, 
  setMarketStatus, 
  setLoading, 
  setError 
} = marketDataSlice.actions;
export default marketDataSlice.reducer;
