"""
Portfolio Management API endpoints
Provides comprehensive portfolio management functionality
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query, BackgroundTasks
from fastapi.security import HTTPBearer
from pydantic import BaseModel, Field, validator
from sqlalchemy import select, and_, or_, desc, func
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from enum import Enum
import asyncio

from app.core.database import get_db, CacheManager, TimeSeriesManager
from app.core.security import get_current_active_user, require_subscription_tier
from app.models.user import User
from app.models.portfolio import Portfolio
from app.models.trade import Trade, Position, OrderStatus
from app.models.user import BrokerAccount
from app.models.stock import Stock
from app.models.portfolio import Strategy
from app.utils.rate_limiter import RateLimiter
from app.core.config import settings

router = APIRouter()
security = HTTPBearer()

# Rate limiter for portfolio operations
portfolio_rate_limiter = RateLimiter(
    max_requests=200,  # Free tier: 200 requests per hour
    time_window=3600,
    premium_max_requests=1000,  # Premium tier: 1000 requests per hour
    enterprise_max_requests=5000  # Enterprise tier: 5000 requests per hour
)

# Enums
class PerformancePeriod(str, Enum):
    DAY = "1d"
    WEEK = "1w"
    MONTH = "1m"
    QUARTER = "3m"
    YEAR = "1y"
    ALL = "all"

class RebalanceFrequency(str, Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"

# Pydantic models
class PortfolioCreate(BaseModel):
    """Create new portfolio"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    initial_capital: float = Field(..., gt=0, le=10000000)
    max_position_size: float = Field(default=0.1, gt=0, le=1.0)
    max_daily_loss: float = Field(default=0.05, gt=0, le=1.0)
    max_total_risk: float = Field(default=0.2, gt=0, le=1.0)
    is_paper_trading: bool = Field(default=True)
    auto_rebalance: bool = Field(default=False)
    rebalance_frequency: Optional[RebalanceFrequency] = Field(default=None)

class PortfolioUpdate(BaseModel):
    """Update portfolio"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    max_position_size: Optional[float] = Field(None, gt=0, le=1.0)
    max_daily_loss: Optional[float] = Field(None, gt=0, le=1.0)
    max_total_risk: Optional[float] = Field(None, gt=0, le=1.0)
    is_active: Optional[bool] = None
    auto_rebalance: Optional[bool] = None
    rebalance_frequency: Optional[RebalanceFrequency] = None

class PortfolioResponse(BaseModel):
    """Portfolio response model"""
    id: int
    name: str
    description: Optional[str]
    initial_capital: float
    current_capital: float
    available_cash: float
    market_value: float
    invested_amount: float
    cash_percentage: float
    max_position_size: float
    max_daily_loss: float
    max_total_risk: float
    total_return: float
    total_return_percent: float
    day_return: float
    day_return_percent: float
    sharpe_ratio: Optional[float]
    max_drawdown: Optional[float]
    is_active: bool
    is_paper_trading: bool
    auto_rebalance: bool
    rebalance_frequency: Optional[str]
    created_at: str
    updated_at: Optional[str]
    last_rebalanced: Optional[str]

class PerformanceMetrics(BaseModel):
    """Portfolio performance metrics"""
    period: str
    start_date: str
    end_date: str
    total_return: float
    total_return_percent: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    total_trades: int
    winning_trades: int
    losing_trades: int

class AllocationTarget(BaseModel):
    """Portfolio allocation target"""
    symbol: str
    target_percentage: float = Field(..., ge=0, le=100)
    min_percentage: Optional[float] = Field(None, ge=0, le=100)
    max_percentage: Optional[float] = Field(None, ge=0, le=100)

class RebalanceRequest(BaseModel):
    """Portfolio rebalance request"""
    allocation_targets: List[AllocationTarget]
    force_rebalance: bool = Field(default=False)
    dry_run: bool = Field(default=False)

class RebalanceResponse(BaseModel):
    """Portfolio rebalance response"""
    portfolio_id: int
    rebalance_id: str
    status: str
    target_allocations: List[Dict[str, Any]]
    current_allocations: List[Dict[str, Any]]
    required_trades: List[Dict[str, Any]]
    estimated_cost: float
    dry_run: bool
    created_at: str
    executed_at: Optional[str]

@router.get("/portfolios", response_model=List[PortfolioResponse])
async def get_portfolios(
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    active_only: bool = Query(True)
):
    """Get user's portfolios"""
    async with get_db() as db:
        query = select(Portfolio).where(Portfolio.user_id == current_user.id)
        
        if active_only:
            query = query.where(Portfolio.is_active == True)
        
        query = query.order_by(desc(Portfolio.created_at)).offset(skip).limit(limit)
        
        result = await db.execute(query)
        portfolios = result.scalars().all()
        
        return [
            PortfolioResponse(
                id=portfolio.id,
                name=portfolio.name,
                description=portfolio.description,
                initial_capital=portfolio.initial_capital,
                current_capital=portfolio.current_capital,
                available_cash=portfolio.available_cash,
                market_value=portfolio.market_value,
                invested_amount=portfolio.invested_amount,
                cash_percentage=portfolio.cash_percentage,
                max_position_size=portfolio.max_position_size,
                max_daily_loss=portfolio.max_daily_loss,
                max_total_risk=portfolio.max_total_risk,
                total_return=portfolio.total_return,
                total_return_percent=portfolio.total_return_percent,
                day_return=portfolio.day_return,
                day_return_percent=portfolio.day_return_percent,
                sharpe_ratio=portfolio.sharpe_ratio,
                max_drawdown=portfolio.max_drawdown,
                is_active=portfolio.is_active,
                is_paper_trading=portfolio.is_paper_trading,
                auto_rebalance=portfolio.auto_rebalance,
                rebalance_frequency=portfolio.rebalance_frequency,
                created_at=portfolio.created_at.isoformat(),
                updated_at=portfolio.updated_at.isoformat() if portfolio.updated_at else None,
                last_rebalanced=portfolio.last_rebalanced.isoformat() if portfolio.last_rebalanced else None
            )
            for portfolio in portfolios
        ]

@router.post("/portfolios", response_model=PortfolioResponse, status_code=status.HTTP_201_CREATED)
async def create_portfolio(
    portfolio_data: PortfolioCreate,
    current_user: User = Depends(get_current_active_user)
):
    """Create new portfolio"""
    # Check subscription limits
    if current_user.subscription_tier == "free":
        async with get_db() as db:
            result = await db.execute(
                select(Portfolio).where(Portfolio.user_id == current_user.id)
            )
            existing_portfolios = result.scalars().all()
            if len(existing_portfolios) >= 2:  # Free tier limit
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Free tier limited to 2 portfolios. Upgrade to create more."
                )
    
    async with get_db() as db:
        # Check for duplicate portfolio name
        result = await db.execute(
            select(Portfolio).where(
                and_(
                    Portfolio.user_id == current_user.id,
                    Portfolio.name == portfolio_data.name
                )
            )
        )
        if result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Portfolio with this name already exists"
            )
        
        # Create new portfolio
        new_portfolio = Portfolio(
            user_id=current_user.id,
            name=portfolio_data.name,
            description=portfolio_data.description,
            initial_capital=portfolio_data.initial_capital,
            current_capital=portfolio_data.initial_capital,
            available_cash=portfolio_data.initial_capital,
            max_position_size=portfolio_data.max_position_size,
            max_daily_loss=portfolio_data.max_daily_loss,
            max_total_risk=portfolio_data.max_total_risk,
            is_paper_trading=portfolio_data.is_paper_trading,
            auto_rebalance=portfolio_data.auto_rebalance,
            rebalance_frequency=portfolio_data.rebalance_frequency.value if portfolio_data.rebalance_frequency else None
        )
        
        db.add(new_portfolio)
        await db.commit()
        await db.refresh(new_portfolio)
        
        return PortfolioResponse(
            id=new_portfolio.id,
            name=new_portfolio.name,
            description=new_portfolio.description,
            initial_capital=new_portfolio.initial_capital,
            current_capital=new_portfolio.current_capital,
            available_cash=new_portfolio.available_cash,
            market_value=new_portfolio.market_value,
            invested_amount=new_portfolio.invested_amount,
            cash_percentage=new_portfolio.cash_percentage,
            max_position_size=new_portfolio.max_position_size,
            max_daily_loss=new_portfolio.max_daily_loss,
            max_total_risk=new_portfolio.max_total_risk,
            total_return=new_portfolio.total_return,
            total_return_percent=new_portfolio.total_return_percent,
            day_return=new_portfolio.day_return,
            day_return_percent=new_portfolio.day_return_percent,
            sharpe_ratio=new_portfolio.sharpe_ratio,
            max_drawdown=new_portfolio.max_drawdown,
            is_active=new_portfolio.is_active,
            is_paper_trading=new_portfolio.is_paper_trading,
            auto_rebalance=new_portfolio.auto_rebalance,
            rebalance_frequency=new_portfolio.rebalance_frequency,
            created_at=new_portfolio.created_at.isoformat(),
            updated_at=new_portfolio.updated_at.isoformat() if new_portfolio.updated_at else None,
            last_rebalanced=new_portfolio.last_rebalanced.isoformat() if new_portfolio.last_rebalanced else None
        )

@router.get("/portfolios/{portfolio_id}", response_model=PortfolioResponse)
async def get_portfolio(
    portfolio_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Get specific portfolio"""
    async with get_db() as db:
        result = await db.execute(
            select(Portfolio).where(
                and_(
                    Portfolio.id == portfolio_id,
                    Portfolio.user_id == current_user.id
                )
            )
        )
        portfolio = result.scalar_one_or_none()

        if not portfolio:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Portfolio not found"
            )

        return PortfolioResponse(
            id=portfolio.id,
            name=portfolio.name,
            description=portfolio.description,
            initial_capital=portfolio.initial_capital,
            current_capital=portfolio.current_capital,
            available_cash=portfolio.available_cash,
            market_value=portfolio.market_value,
            invested_amount=portfolio.invested_amount,
            cash_percentage=portfolio.cash_percentage,
            max_position_size=portfolio.max_position_size,
            max_daily_loss=portfolio.max_daily_loss,
            max_total_risk=portfolio.max_total_risk,
            total_return=portfolio.total_return,
            total_return_percent=portfolio.total_return_percent,
            day_return=portfolio.day_return,
            day_return_percent=portfolio.day_return_percent,
            sharpe_ratio=portfolio.sharpe_ratio,
            max_drawdown=portfolio.max_drawdown,
            is_active=portfolio.is_active,
            is_paper_trading=portfolio.is_paper_trading,
            auto_rebalance=portfolio.auto_rebalance,
            rebalance_frequency=portfolio.rebalance_frequency,
            created_at=portfolio.created_at.isoformat(),
            updated_at=portfolio.updated_at.isoformat() if portfolio.updated_at else None,
            last_rebalanced=portfolio.last_rebalanced.isoformat() if portfolio.last_rebalanced else None
        )

@router.put("/portfolios/{portfolio_id}", response_model=PortfolioResponse)
async def update_portfolio(
    portfolio_id: int,
    portfolio_data: PortfolioUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """Update portfolio"""
    async with get_db() as db:
        result = await db.execute(
            select(Portfolio).where(
                and_(
                    Portfolio.id == portfolio_id,
                    Portfolio.user_id == current_user.id
                )
            )
        )
        portfolio = result.scalar_one_or_none()

        if not portfolio:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Portfolio not found"
            )

        # Update fields if provided
        update_data = portfolio_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if field == "rebalance_frequency" and value:
                setattr(portfolio, field, value.value)
            else:
                setattr(portfolio, field, value)

        portfolio.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(portfolio)

        return PortfolioResponse(
            id=portfolio.id,
            name=portfolio.name,
            description=portfolio.description,
            initial_capital=portfolio.initial_capital,
            current_capital=portfolio.current_capital,
            available_cash=portfolio.available_cash,
            market_value=portfolio.market_value,
            invested_amount=portfolio.invested_amount,
            cash_percentage=portfolio.cash_percentage,
            max_position_size=portfolio.max_position_size,
            max_daily_loss=portfolio.max_daily_loss,
            max_total_risk=portfolio.max_total_risk,
            total_return=portfolio.total_return,
            total_return_percent=portfolio.total_return_percent,
            day_return=portfolio.day_return,
            day_return_percent=portfolio.day_return_percent,
            sharpe_ratio=portfolio.sharpe_ratio,
            max_drawdown=portfolio.max_drawdown,
            is_active=portfolio.is_active,
            is_paper_trading=portfolio.is_paper_trading,
            auto_rebalance=portfolio.auto_rebalance,
            rebalance_frequency=portfolio.rebalance_frequency,
            created_at=portfolio.created_at.isoformat(),
            updated_at=portfolio.updated_at.isoformat() if portfolio.updated_at else None,
            last_rebalanced=portfolio.last_rebalanced.isoformat() if portfolio.last_rebalanced else None
        )

@router.delete("/portfolios/{portfolio_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_portfolio(
    portfolio_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Delete portfolio"""
    async with get_db() as db:
        result = await db.execute(
            select(Portfolio).where(
                and_(
                    Portfolio.id == portfolio_id,
                    Portfolio.user_id == current_user.id
                )
            )
        )
        portfolio = result.scalar_one_or_none()

        if not portfolio:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Portfolio not found"
            )

        # Check if portfolio has active positions
        result = await db.execute(
            select(Position).where(
                and_(
                    Position.portfolio_id == portfolio_id,
                    Position.quantity != 0
                )
            )
        )
        active_positions = result.scalars().all()

        if active_positions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete portfolio with active positions"
            )

        await db.delete(portfolio)
        await db.commit()

@router.get("/portfolios/{portfolio_id}/performance", response_model=PerformanceMetrics)
async def get_portfolio_performance(
    portfolio_id: int,
    current_user: User = Depends(get_current_active_user),
    period: PerformancePeriod = Query(PerformancePeriod.MONTH)
):
    """Get portfolio performance metrics"""
    # Apply rate limiting
    await portfolio_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier
    )

    async with get_db() as db:
        # Verify portfolio ownership
        result = await db.execute(
            select(Portfolio).where(
                and_(
                    Portfolio.id == portfolio_id,
                    Portfolio.user_id == current_user.id
                )
            )
        )
        portfolio = result.scalar_one_or_none()

        if not portfolio:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Portfolio not found"
            )

        # Calculate date range
        end_date = datetime.utcnow()
        if period == PerformancePeriod.DAY:
            start_date = end_date - timedelta(days=1)
        elif period == PerformancePeriod.WEEK:
            start_date = end_date - timedelta(weeks=1)
        elif period == PerformancePeriod.MONTH:
            start_date = end_date - timedelta(days=30)
        elif period == PerformancePeriod.QUARTER:
            start_date = end_date - timedelta(days=90)
        elif period == PerformancePeriod.YEAR:
            start_date = end_date - timedelta(days=365)
        else:  # ALL
            start_date = portfolio.created_at

        # Get trades in the period
        result = await db.execute(
            select(Trade).where(
                and_(
                    Trade.portfolio_id == portfolio_id,
                    Trade.status == OrderStatus.FILLED,
                    Trade.filled_at >= start_date,
                    Trade.filled_at <= end_date
                )
            )
        )
        trades = result.scalars().all()

        # Calculate performance metrics
        total_trades = len(trades)
        winning_trades = len([t for t in trades if (t.realized_pnl or 0) > 0])
        losing_trades = len([t for t in trades if (t.realized_pnl or 0) < 0])

        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

        total_pnl = sum(t.realized_pnl or 0 for t in trades)
        total_wins = sum(t.realized_pnl for t in trades if (t.realized_pnl or 0) > 0)
        total_losses = sum(abs(t.realized_pnl) for t in trades if (t.realized_pnl or 0) < 0)

        avg_win = (total_wins / winning_trades) if winning_trades > 0 else 0
        avg_loss = (total_losses / losing_trades) if losing_trades > 0 else 0
        profit_factor = (total_wins / total_losses) if total_losses > 0 else 0

        # Calculate returns
        initial_value = portfolio.initial_capital
        current_value = portfolio.current_capital + portfolio.market_value
        total_return = current_value - initial_value
        total_return_percent = (total_return / initial_value * 100) if initial_value > 0 else 0

        # Annualized return (simplified)
        days_elapsed = (end_date - start_date).days
        annualized_return = (total_return_percent * 365 / days_elapsed) if days_elapsed > 0 else 0

        # Simplified metrics (would be calculated from historical data)
        volatility = 15.0  # 15% annualized volatility estimate
        sharpe_ratio = portfolio.sharpe_ratio or 1.0
        max_drawdown = portfolio.max_drawdown or 0.05

        return PerformanceMetrics(
            period=period.value,
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            total_return=total_return,
            total_return_percent=total_return_percent,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            avg_win=avg_win,
            avg_loss=avg_loss,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades
        )
