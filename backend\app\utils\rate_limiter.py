"""
Rate limiting utilities for API endpoints
Provides comprehensive rate limiting with subscription tier support
"""

import asyncio
import time
from typing import Dict, Optional
from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Ex<PERSON>, status
import redis.asyncio as redis

from app.core.config import settings
from app.core.database import get_redis


class RateLimiter:
    """
    Advanced rate limiter with subscription tier support
    Uses Redis for distributed rate limiting
    """
    
    def __init__(
        self,
        max_requests: int,
        time_window: int,
        premium_max_requests: Optional[int] = None,
        enterprise_max_requests: Optional[int] = None,
        burst_allowance: float = 1.2  # Allow 20% burst
    ):
        self.max_requests = max_requests
        self.time_window = time_window
        self.premium_max_requests = premium_max_requests or max_requests * 5
        self.enterprise_max_requests = enterprise_max_requests or max_requests * 20
        self.burst_allowance = burst_allowance
        
        # In-memory fallback for when Redis is unavailable
        self._memory_store: Dict[str, Dict] = {}
        self._cleanup_interval = 300  # Clean up every 5 minutes
        self._last_cleanup = time.time()
    
    def _get_rate_limit_for_tier(self, subscription_tier: str) -> int:
        """Get rate limit based on subscription tier"""
        if subscription_tier == "enterprise":
            return self.enterprise_max_requests
        elif subscription_tier == "premium":
            return self.premium_max_requests
        else:  # free tier
            return self.max_requests
    
    def _get_redis_key(self, user_id: int, endpoint: str = "default") -> str:
        """Generate Redis key for rate limiting"""
        return f"rate_limit:{user_id}:{endpoint}:{int(time.time() // self.time_window)}"
    
    async def _cleanup_memory_store(self):
        """Clean up expired entries from memory store"""
        current_time = time.time()
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        expired_keys = []
        for key, data in self._memory_store.items():
            if current_time - data.get("window_start", 0) > self.time_window:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._memory_store[key]
        
        self._last_cleanup = current_time
    
    async def _check_rate_limit_redis(
        self,
        user_id: int,
        subscription_tier: str,
        endpoint: str = "default"
    ) -> Dict[str, any]:
        """Check rate limit using Redis"""
        try:
            redis_client = await get_redis()
            key = self._get_redis_key(user_id, endpoint)
            
            # Get current count
            current_count = await redis_client.get(key)
            current_count = int(current_count) if current_count else 0
            
            # Get rate limit for user's tier
            rate_limit = self._get_rate_limit_for_tier(subscription_tier)
            burst_limit = int(rate_limit * self.burst_allowance)
            
            # Check if limit exceeded
            if current_count >= burst_limit:
                # Get TTL for reset time
                ttl = await redis_client.ttl(key)
                reset_time = datetime.utcnow() + timedelta(seconds=ttl if ttl > 0 else self.time_window)
                
                return {
                    "allowed": False,
                    "current_count": current_count,
                    "rate_limit": rate_limit,
                    "burst_limit": burst_limit,
                    "reset_time": reset_time,
                    "retry_after": ttl if ttl > 0 else self.time_window
                }
            
            # Increment counter
            pipe = redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, self.time_window)
            await pipe.execute()
            
            return {
                "allowed": True,
                "current_count": current_count + 1,
                "rate_limit": rate_limit,
                "burst_limit": burst_limit,
                "reset_time": datetime.utcnow() + timedelta(seconds=self.time_window),
                "retry_after": 0
            }
            
        except Exception as e:
            # Fallback to memory-based rate limiting
            return await self._check_rate_limit_memory(user_id, subscription_tier, endpoint)
    
    async def _check_rate_limit_memory(
        self,
        user_id: int,
        subscription_tier: str,
        endpoint: str = "default"
    ) -> Dict[str, any]:
        """Fallback memory-based rate limiting"""
        await self._cleanup_memory_store()
        
        current_time = time.time()
        window_start = int(current_time // self.time_window) * self.time_window
        key = f"{user_id}:{endpoint}:{window_start}"
        
        # Get or create entry
        if key not in self._memory_store:
            self._memory_store[key] = {
                "count": 0,
                "window_start": window_start
            }
        
        entry = self._memory_store[key]
        rate_limit = self._get_rate_limit_for_tier(subscription_tier)
        burst_limit = int(rate_limit * self.burst_allowance)
        
        # Check if limit exceeded
        if entry["count"] >= burst_limit:
            reset_time = datetime.utcnow() + timedelta(
                seconds=self.time_window - (current_time - window_start)
            )
            return {
                "allowed": False,
                "current_count": entry["count"],
                "rate_limit": rate_limit,
                "burst_limit": burst_limit,
                "reset_time": reset_time,
                "retry_after": int(self.time_window - (current_time - window_start))
            }
        
        # Increment counter
        entry["count"] += 1
        
        return {
            "allowed": True,
            "current_count": entry["count"],
            "rate_limit": rate_limit,
            "burst_limit": burst_limit,
            "reset_time": datetime.utcnow() + timedelta(
                seconds=self.time_window - (current_time - window_start)
            ),
            "retry_after": 0
        }
    
    async def check_rate_limit(
        self,
        user_id: int,
        subscription_tier: str = "free",
        endpoint: str = "default"
    ) -> Dict[str, any]:
        """
        Check if user has exceeded rate limit
        
        Args:
            user_id: User ID
            subscription_tier: User's subscription tier (free, premium, enterprise)
            endpoint: Endpoint identifier for separate rate limiting
            
        Returns:
            Dict with rate limit information
            
        Raises:
            HTTPException: If rate limit is exceeded
        """
        try:
            result = await self._check_rate_limit_redis(user_id, subscription_tier, endpoint)
        except Exception:
            result = await self._check_rate_limit_memory(user_id, subscription_tier, endpoint)
        
        if not result["allowed"]:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Rate limit exceeded",
                    "current_count": result["current_count"],
                    "rate_limit": result["rate_limit"],
                    "reset_time": result["reset_time"].isoformat(),
                    "retry_after": result["retry_after"],
                    "subscription_tier": subscription_tier,
                    "upgrade_message": (
                        "Upgrade your subscription for higher rate limits"
                        if subscription_tier == "free" else None
                    )
                },
                headers={
                    "X-RateLimit-Limit": str(result["rate_limit"]),
                    "X-RateLimit-Remaining": str(max(0, result["rate_limit"] - result["current_count"])),
                    "X-RateLimit-Reset": str(int(result["reset_time"].timestamp())),
                    "Retry-After": str(result["retry_after"])
                }
            )
        
        return result
    
    async def get_rate_limit_info(
        self,
        user_id: int,
        subscription_tier: str = "free",
        endpoint: str = "default"
    ) -> Dict[str, any]:
        """Get current rate limit information without incrementing counter"""
        try:
            redis_client = await get_redis()
            key = self._get_redis_key(user_id, endpoint)
            
            current_count = await redis_client.get(key)
            current_count = int(current_count) if current_count else 0
            
            rate_limit = self._get_rate_limit_for_tier(subscription_tier)
            ttl = await redis_client.ttl(key)
            
            return {
                "current_count": current_count,
                "rate_limit": rate_limit,
                "remaining": max(0, rate_limit - current_count),
                "reset_time": datetime.utcnow() + timedelta(seconds=ttl if ttl > 0 else self.time_window),
                "subscription_tier": subscription_tier
            }
            
        except Exception:
            # Fallback to memory store
            current_time = time.time()
            window_start = int(current_time // self.time_window) * self.time_window
            key = f"{user_id}:{endpoint}:{window_start}"
            
            current_count = self._memory_store.get(key, {}).get("count", 0)
            rate_limit = self._get_rate_limit_for_tier(subscription_tier)
            
            return {
                "current_count": current_count,
                "rate_limit": rate_limit,
                "remaining": max(0, rate_limit - current_count),
                "reset_time": datetime.utcnow() + timedelta(
                    seconds=self.time_window - (current_time - window_start)
                ),
                "subscription_tier": subscription_tier
            }


# Pre-configured rate limiters for different endpoints
api_rate_limiter = RateLimiter(
    max_requests=1000,  # Free: 1000 requests per hour
    time_window=3600,
    premium_max_requests=5000,  # Premium: 5000 requests per hour
    enterprise_max_requests=50000  # Enterprise: 50000 requests per hour
)

auth_rate_limiter = RateLimiter(
    max_requests=10,  # 10 auth attempts per 15 minutes
    time_window=900,
    premium_max_requests=20,
    enterprise_max_requests=50
)

data_rate_limiter = RateLimiter(
    max_requests=500,  # Free: 500 data requests per hour
    time_window=3600,
    premium_max_requests=5000,  # Premium: 5000 requests per hour
    enterprise_max_requests=50000  # Enterprise: 50000 requests per hour
)
