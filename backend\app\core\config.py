"""
Configuration settings for MarketHawk application
"""

from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import List, Optional
import os
from pathlib import Path

class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "MarketHawk"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Security
    SECRET_KEY: str = Field(default="dev-secret-key-change-in-production", env="SECRET_KEY")
    ALGORITHM: str = Field(default="HS256", env="ALGORITHM")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")

    # Database
    DATABASE_URL: str = Field(default="sqlite+aiosqlite:///./markethawk.db", env="DATABASE_URL")
    INFLUXDB_URL: str = Field(default="http://localhost:8086", env="INFLUXDB_URL")
    INFLUXDB_TOKEN: str = Field(default="development-token", env="INFLUXDB_TOKEN")
    INFLUXDB_ORG: str = Field(default="markethawk", env="INFLUXDB_ORG")
    INFLUXDB_BUCKET: str = Field(default="market_data", env="INFLUXDB_BUCKET")
    
    # Redis
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # CORS
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000"],
        env="CORS_ORIGINS"
    )
    
    # API Keys - Free Tier
    ALPHA_VANTAGE_API_KEY: Optional[str] = Field(default=None, env="ALPHA_VANTAGE_API_KEY")
    POLYGON_API_KEY: Optional[str] = Field(default=None, env="POLYGON_API_KEY")
    YAHOO_FINANCE_API_KEY: Optional[str] = Field(default=None, env="YAHOO_FINANCE_API_KEY")
    FRED_API_KEY: Optional[str] = Field(default=None, env="FRED_API_KEY")
    NEWS_API_KEY: Optional[str] = Field(default=None, env="NEWS_API_KEY")
    IEX_API_KEY: Optional[str] = Field(default=None, env="IEX_API_KEY")
    
    # Broker API Keys
    TD_AMERITRADE_CLIENT_ID: Optional[str] = Field(default=None, env="TD_AMERITRADE_CLIENT_ID")
    TD_AMERITRADE_REDIRECT_URI: str = Field(
        default="http://localhost:8000/auth/td/callback",
        env="TD_AMERITRADE_REDIRECT_URI"
    )
    
    INTERACTIVE_BROKERS_HOST: str = Field(default="localhost", env="INTERACTIVE_BROKERS_HOST")
    INTERACTIVE_BROKERS_PORT: int = Field(default=7497, env="INTERACTIVE_BROKERS_PORT")
    
    ALPACA_API_KEY: Optional[str] = Field(default=None, env="ALPACA_API_KEY")
    ALPACA_SECRET_KEY: Optional[str] = Field(default=None, env="ALPACA_SECRET_KEY")
    ALPACA_BASE_URL: str = Field(
        default="https://paper-api.alpaca.markets",
        env="ALPACA_BASE_URL"
    )
    
    # Email Configuration
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USER: Optional[str] = Field(default=None, env="SMTP_USER")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    
    # Monitoring
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    PROMETHEUS_PORT: int = Field(default=9090, env="PROMETHEUS_PORT")
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=100, env="RATE_LIMIT_PER_MINUTE")
    RATE_LIMIT_BURST: int = Field(default=20, env="RATE_LIMIT_BURST")
    
    # Trading Configuration
    DEFAULT_POSITION_SIZE: float = Field(default=0.02, env="DEFAULT_POSITION_SIZE")
    MAX_POSITION_SIZE: float = Field(default=0.10, env="MAX_POSITION_SIZE")
    MAX_DAILY_LOSS: float = Field(default=0.05, env="MAX_DAILY_LOSS")
    PAPER_TRADING: bool = Field(default=True, env="PAPER_TRADING")
    
    # Data Provider Settings
    DATA_UPDATE_INTERVAL: int = Field(default=60, env="DATA_UPDATE_INTERVAL")  # seconds
    SCREENING_INTERVAL: int = Field(default=300, env="SCREENING_INTERVAL")  # seconds
    MAX_CONCURRENT_REQUESTS: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    
    # Risk Management
    MAX_PORTFOLIO_RISK: float = Field(default=0.20, env="MAX_PORTFOLIO_RISK")
    MAX_SECTOR_CONCENTRATION: float = Field(default=0.30, env="MAX_SECTOR_CONCENTRATION")
    MAX_POSITION_SIZE: float = Field(default=0.10, env="MAX_POSITION_SIZE")
    MAX_DAILY_LOSS: float = Field(default=0.05, env="MAX_DAILY_LOSS")
    STOP_LOSS_PERCENTAGE: float = Field(default=0.05, env="STOP_LOSS_PERCENTAGE")
    TAKE_PROFIT_PERCENTAGE: float = Field(default=0.10, env="TAKE_PROFIT_PERCENTAGE")
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        """Parse CORS origins from string or list"""
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        """Validate log level"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    @validator("DEFAULT_POSITION_SIZE", "MAX_POSITION_SIZE", "MAX_DAILY_LOSS")
    def validate_percentages(cls, v):
        """Validate percentage values"""
        if not 0 < v <= 1:
            raise ValueError("Percentage values must be between 0 and 1")
        return v
    
    @validator("MAX_POSITION_SIZE")
    def validate_max_position_size(cls, v, values):
        """Validate max position size is greater than default"""
        if "DEFAULT_POSITION_SIZE" in values and v < values["DEFAULT_POSITION_SIZE"]:
            raise ValueError("MAX_POSITION_SIZE must be greater than DEFAULT_POSITION_SIZE")
        return v
    
    class Config:
        env_file = "../.env"  # Look for .env in parent directory
        case_sensitive = True

# Create settings instance
settings = Settings()

# Derived settings
class DerivedSettings:
    """Derived configuration settings"""
    
    @staticmethod
    def get_database_config():
        """Get database configuration"""
        config = {
            "url": settings.DATABASE_URL,
            "echo": settings.DEBUG,
        }

        # Add PostgreSQL-specific settings only if using PostgreSQL
        if "postgresql" in settings.DATABASE_URL:
            config.update({
                "pool_size": 20,
                "max_overflow": 30,
                "pool_pre_ping": True,
                "pool_recycle": 3600,
            })

        return config
    
    @staticmethod
    def get_influxdb_config():
        """Get InfluxDB configuration"""
        return {
            "url": settings.INFLUXDB_URL,
            "token": settings.INFLUXDB_TOKEN,
            "org": settings.INFLUXDB_ORG,
            "bucket": settings.INFLUXDB_BUCKET,
        }
    
    @staticmethod
    def get_redis_config():
        """Get Redis configuration"""
        return {
            "url": settings.REDIS_URL,
            "decode_responses": True,
            "socket_keepalive": True,
            "socket_keepalive_options": {},
            "health_check_interval": 30,
        }
    
    @staticmethod
    def get_broker_configs():
        """Get broker configurations"""
        return {
            "td_ameritrade": {
                "client_id": settings.TD_AMERITRADE_CLIENT_ID,
                "redirect_uri": settings.TD_AMERITRADE_REDIRECT_URI,
                "paper_trading": settings.PAPER_TRADING,
            },
            "interactive_brokers": {
                "host": settings.INTERACTIVE_BROKERS_HOST,
                "port": settings.INTERACTIVE_BROKERS_PORT,
                "paper_trading": settings.PAPER_TRADING,
            },
            "alpaca": {
                "api_key": settings.ALPACA_API_KEY,
                "secret_key": settings.ALPACA_SECRET_KEY,
                "base_url": settings.ALPACA_BASE_URL,
                "paper_trading": settings.PAPER_TRADING,
            }
        }
    
    @staticmethod
    def get_data_provider_configs():
        """Get data provider configurations"""
        return {
            "alpha_vantage": {
                "api_key": settings.ALPHA_VANTAGE_API_KEY,
                "rate_limit": 5,  # requests per minute for free tier
            },
            "polygon": {
                "api_key": settings.POLYGON_API_KEY,
                "rate_limit": 5,  # requests per minute for free tier
            },
            "yahoo_finance": {
                "api_key": settings.YAHOO_FINANCE_API_KEY,
                "rate_limit": 2000,  # requests per hour
            },
            "fred": {
                "api_key": settings.FRED_API_KEY,
                "rate_limit": 120,  # requests per minute
            }
        }

derived_settings = DerivedSettings()
