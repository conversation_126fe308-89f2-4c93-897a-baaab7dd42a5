"""
Technical indicators for stock analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import logging

logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """
    Comprehensive technical indicators calculator
    Pure Python implementation for reliability
    """

    @staticmethod
    def sma(data: pd.Series, period: int) -> pd.Series:
        """Simple Moving Average"""
        return data.rolling(window=period).mean()

    @staticmethod
    def ema(data: pd.Series, period: int) -> pd.Series:
        """Exponential Moving Average"""
        return data.ewm(span=period, adjust=False).mean()

    @staticmethod
    def rsi(data: pd.Series, period: int = 14) -> pd.Series:
        """Relative Strength Index"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """MACD (Moving Average Convergence Divergence)"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line

        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }

    @staticmethod
    def bollinger_bands(data: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """Bollinger Bands"""
        sma = TechnicalIndicators.sma(data, period)
        std = data.rolling(window=period).std()

        return {
            'upper': sma + (std * std_dev),
            'middle': sma,
            'lower': sma - (std * std_dev)
        }

    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series,
                   k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """Stochastic Oscillator"""
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()

        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()

        return {
            'k': k_percent,
            'd': d_percent
        }

    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Average True Range"""
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())

        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return true_range.rolling(window=period).mean()

    @staticmethod
    def williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Williams %R"""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()

        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        return williams_r

    @staticmethod
    def cci(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Commodity Channel Index"""
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mean_deviation = typical_price.rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - x.mean()))
        )

        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        return cci

    @staticmethod
    def adx(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """Average Directional Index"""
        # Calculate True Range
        tr = TechnicalIndicators.atr(high, low, close, 1)

        # Calculate Directional Movement
        dm_plus = np.where((high.diff() > low.diff().abs()) & (high.diff() > 0), high.diff(), 0)
        dm_minus = np.where((low.diff().abs() > high.diff()) & (low.diff() < 0), low.diff().abs(), 0)

        dm_plus = pd.Series(dm_plus, index=high.index)
        dm_minus = pd.Series(dm_minus, index=low.index)

        # Smooth the values
        tr_smooth = tr.rolling(window=period).mean()
        dm_plus_smooth = dm_plus.rolling(window=period).mean()
        dm_minus_smooth = dm_minus.rolling(window=period).mean()

        # Calculate DI+ and DI-
        di_plus = 100 * (dm_plus_smooth / tr_smooth)
        di_minus = 100 * (dm_minus_smooth / tr_smooth)

        # Calculate DX
        dx = 100 * np.abs(di_plus - di_minus) / (di_plus + di_minus)

        # Calculate ADX
        adx = dx.rolling(window=period).mean()
        return adx

    @staticmethod
    def obv(close: pd.Series, volume: pd.Series) -> pd.Series:
        """On-Balance Volume"""
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]

        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]

        return obv

    @staticmethod
    def vwap(high: pd.Series, low: pd.Series, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Volume Weighted Average Price"""
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        return vwap

    @staticmethod
    def momentum(data: pd.Series, period: int = 10) -> pd.Series:
        """Price Momentum"""
        return data.diff(period)

    @staticmethod
    def roc(data: pd.Series, period: int = 10) -> pd.Series:
        """Rate of Change"""
        return ((data / data.shift(period)) - 1) * 100

    @classmethod
    async def calculate_all_indicators(cls, df: pd.DataFrame) -> Dict[str, Union[float, Dict[str, float]]]:
        """
        Calculate all technical indicators for a DataFrame

        Args:
            df: DataFrame with OHLCV data (columns: open, high, low, close, volume)

        Returns:
            Dictionary with all calculated indicators
        """
        if len(df) < 50:  # Need enough data for calculations
            return {}

        indicators = {}

        try:
            # Ensure we have the required columns
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                logger.warning(f"Missing required columns. Available: {df.columns.tolist()}")
                return {}

            # Price data
            close = df['close']
            high = df['high']
            low = df['low']
            open_price = df['open']
            volume = df['volume']

            # Moving averages
            sma_20 = cls.sma(close, 20)
            sma_50 = cls.sma(close, 50)
            sma_200 = cls.sma(close, 200)
            ema_12 = cls.ema(close, 12)
            ema_26 = cls.ema(close, 26)

            indicators['sma_20'] = sma_20.iloc[-1] if not pd.isna(sma_20.iloc[-1]) else None
            indicators['sma_50'] = sma_50.iloc[-1] if not pd.isna(sma_50.iloc[-1]) else None
            indicators['sma_200'] = sma_200.iloc[-1] if not pd.isna(sma_200.iloc[-1]) else None
            indicators['ema_12'] = ema_12.iloc[-1] if not pd.isna(ema_12.iloc[-1]) else None
            indicators['ema_26'] = ema_26.iloc[-1] if not pd.isna(ema_26.iloc[-1]) else None

            # RSI
            rsi_values = cls.rsi(close)
            indicators['rsi'] = rsi_values.iloc[-1] if not pd.isna(rsi_values.iloc[-1]) else None

            # MACD
            macd_data = cls.macd(close)
            indicators['macd'] = macd_data['macd'].iloc[-1] if not pd.isna(macd_data['macd'].iloc[-1]) else None
            indicators['macd_signal'] = macd_data['signal'].iloc[-1] if not pd.isna(macd_data['signal'].iloc[-1]) else None
            indicators['macd_histogram'] = macd_data['histogram'].iloc[-1] if not pd.isna(macd_data['histogram'].iloc[-1]) else None

            # Bollinger Bands
            bb_data = cls.bollinger_bands(close)
            indicators['bollinger_upper'] = bb_data['upper'].iloc[-1] if not pd.isna(bb_data['upper'].iloc[-1]) else None
            indicators['bollinger_middle'] = bb_data['middle'].iloc[-1] if not pd.isna(bb_data['middle'].iloc[-1]) else None
            indicators['bollinger_lower'] = bb_data['lower'].iloc[-1] if not pd.isna(bb_data['lower'].iloc[-1]) else None

            # Stochastic
            stoch_data = cls.stochastic(high, low, close)
            indicators['stoch_k'] = stoch_data['k'].iloc[-1] if not pd.isna(stoch_data['k'].iloc[-1]) else None
            indicators['stoch_d'] = stoch_data['d'].iloc[-1] if not pd.isna(stoch_data['d'].iloc[-1]) else None

            # ATR
            atr_values = cls.atr(high, low, close)
            indicators['atr'] = atr_values.iloc[-1] if not pd.isna(atr_values.iloc[-1]) else None

            # Williams %R
            willr_values = cls.williams_r(high, low, close)
            indicators['williams_r'] = willr_values.iloc[-1] if not pd.isna(willr_values.iloc[-1]) else None

            # CCI
            cci_values = cls.cci(high, low, close)
            indicators['cci'] = cci_values.iloc[-1] if not pd.isna(cci_values.iloc[-1]) else None

            # ADX
            adx_values = cls.adx(high, low, close)
            indicators['adx'] = adx_values.iloc[-1] if not pd.isna(adx_values.iloc[-1]) else None

            # OBV
            obv_values = cls.obv(close, volume)
            indicators['obv'] = obv_values.iloc[-1] if not pd.isna(obv_values.iloc[-1]) else None

            # VWAP
            vwap_values = cls.vwap(high, low, close, volume)
            indicators['vwap'] = vwap_values.iloc[-1] if not pd.isna(vwap_values.iloc[-1]) else None

            # Momentum indicators
            momentum_10 = cls.momentum(close, 10)
            indicators['momentum_10'] = momentum_10.iloc[-1] if not pd.isna(momentum_10.iloc[-1]) else None

            roc_10 = cls.roc(close, 10)
            indicators['roc_10'] = roc_10.iloc[-1] if not pd.isna(roc_10.iloc[-1]) else None

            # Price-based indicators
            indicators['price'] = close.iloc[-1]
            indicators['volume'] = volume.iloc[-1]
            indicators['open'] = open_price.iloc[-1]
            indicators['high'] = high.iloc[-1]
            indicators['low'] = low.iloc[-1]

            # Price position relative to moving averages
            if indicators['sma_20']:
                indicators['price_above_sma20'] = close.iloc[-1] > indicators['sma_20']
            if indicators['sma_50']:
                indicators['price_above_sma50'] = close.iloc[-1] > indicators['sma_50']
            if indicators['sma_200']:
                indicators['price_above_sma200'] = close.iloc[-1] > indicators['sma_200']

            # Bollinger Band position
            if indicators['bollinger_upper'] and indicators['bollinger_lower']:
                bb_width = indicators['bollinger_upper'] - indicators['bollinger_lower']
                if bb_width > 0:
                    bb_position = (close.iloc[-1] - indicators['bollinger_lower']) / bb_width
                    indicators['bb_position'] = bb_position
                    indicators['bb_squeeze'] = bb_width < (indicators['bollinger_middle'] * 0.1)  # Squeeze detection

            # Volume analysis
            avg_volume_20 = volume.rolling(20).mean()
            if not pd.isna(avg_volume_20.iloc[-1]):
                indicators['volume_ratio'] = volume.iloc[-1] / avg_volume_20.iloc[-1]
                indicators['high_volume'] = indicators['volume_ratio'] > 1.5

            # Volatility
            returns = close.pct_change()
            volatility_20 = returns.rolling(20).std() * np.sqrt(252)  # Annualized
            indicators['volatility_20'] = volatility_20.iloc[-1] if not pd.isna(volatility_20.iloc[-1]) else None

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return {}

        # Remove None values and convert numpy types to Python types
        clean_indicators = {}
        for k, v in indicators.items():
            if v is not None:
                if isinstance(v, (np.integer, np.floating)):
                    clean_indicators[k] = float(v)
                elif isinstance(v, np.bool_):
                    clean_indicators[k] = bool(v)
                else:
                    clean_indicators[k] = v

        return clean_indicators

