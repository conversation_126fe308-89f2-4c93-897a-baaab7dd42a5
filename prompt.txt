# Build MarketHawk - a Stock Screener App with Automated Trading - Complete Plan 

## App Concept Overview

A comprehensive stock screener that automatically executes trades on multiple brokerages based on predefined screening criteria, technical indicators, and risk management rules. The app combines advanced screening capabilities with automated execution to eliminate emotional trading and ensure consistent strategy implementation.

## Pain Points This App Solves

### 1. Manual Screening Inefficiency
- **Problem**: Traders spend hours manually screening stocks daily
- **Solution**: Automated screening runs continuously, alerting only when criteria are met

### 2. Emotional Trading Decisions
- **Problem**: Fear and greed lead to poor entry/exit timing
- **Solution**: Pre-programmed rules execute trades automatically, removing emotion

### 3. Multi-Platform Management
- **Problem**: Managing accounts across different brokerages is cumbersome
- **Solution**: Single interface to trade across ThinkOrSwim, Interactive Brokers, and others

### 4. Missed Opportunities
- **Problem**: Market moves happen when traders aren't monitoring
- **Solution**: 24/7 monitoring with instant execution when opportunities arise

### 5. Inconsistent Strategy Implementation
- **Problem**: Manual execution leads to strategy drift and inconsistency
- **Solution**: Automated execution ensures perfect strategy adherence

### 6. Complex Options Strategy Management
- **Problem**: Options strategies require precise timing and multiple leg coordination
- **Solution**: Automated multi-leg options execution with risk management

## Stock vs Options Trading Recommendation

### **Recommendation: Support Both with Progression Path**

#### Start with Stocks:
- Lower complexity for initial users
- Better for beginners and conservative traders
- Easier risk management
- More predictable outcomes

#### Add Options for Advanced Users:
- **Covered Calls**: Generate income on existing stock positions
- **Cash-Secured Puts**: Systematic stock acquisition
- **Iron Condors**: Range-bound market strategies
- **Protective Puts**: Downside protection

#### Options Advantages in This App:
- Automated multi-leg strategy execution
- Precise timing for maximum theta decay
- Systematic volatility trading
- Enhanced returns through leverage

## Detailed Requirements

### Core Features

#### 1. Advanced Screening Engine
- **Technical Indicators**: RSI, MACD, Bollinger Bands, Moving Averages
- **Fundamental Metrics**: P/E, P/B, ROE, Debt-to-Equity, Growth rates
- **Custom Formulas**: User-defined screening criteria
- **Real-time Scanning**: Continuous market monitoring
- **Backtesting**: Historical performance validation

#### 2. Broker Integration
- **ThinkOrSwim API**: Full integration with TD Ameritrade platform
- **Interactive Brokers**: TWS API integration
- **Multiple Account Support**: Manage multiple accounts per broker
- **Order Management**: Market, limit, stop, and conditional orders
- **Position Tracking**: Real-time portfolio monitoring

#### 3. Automation Engine
- **Rule-based Trading**: If-then logic for trade execution
- **Risk Management**: Stop losses, position sizing, max drawdown limits
- **Portfolio Rebalancing**: Automatic position adjustments
- **Schedule Trading**: Time-based execution rules
- **Emergency Stops**: Circuit breakers for unusual market conditions

#### 4. Options Trading Features
- **Strategy Builder**: Visual options strategy construction
- **Multi-leg Execution**: Simultaneous options combinations
- **Greeks Monitoring**: Real-time risk metrics
- **Volatility Analysis**: IV percentile and skew monitoring
- **Assignment Management**: Automatic handling of option assignments

#### 5. Risk Management
- **Position Sizing**: Kelly criterion, fixed fractional, volatility-based
- **Correlation Analysis**: Avoid over-concentration in related assets
- **Drawdown Protection**: Automatic trading suspension triggers
- **Margin Management**: Real-time margin requirement monitoring
- **VaR Calculations**: Value at Risk analysis

#### 6. User Interface
- **Dashboard**: Real-time portfolio overview
- **Strategy Builder**: Drag-and-drop rule creation
- **Backtesting Suite**: Historical performance analysis
- **Alert System**: Custom notifications and warnings
- **Mobile App**: iOS/Android for monitoring on-the-go

### Technical Requirements

#### Backend Architecture
- **Cloud-based**: AWS/Azure for scalability
- **Real-time Data**: WebSocket connections for live market data
- **Database**: Time-series database for historical data
- **Security**: End-to-end encryption for API keys and trades
- **Redundancy**: Multiple data centers for reliability

#### Data Requirements
- **Market Data**: Level 1 quotes, historical data, options chains
- **Fundamental Data**: Earnings, financials, analyst ratings
- **Alternative Data**: Social sentiment, news sentiment, insider trading
- **Economic Data**: Fed rates, economic indicators, earnings calendars

#### Compliance & Security
- **SEC Compliance**: Automated trading regulations
- **Broker Compliance**: Each broker's specific requirements
- **Data Security**: SOC 2 Type II certification
- **Audit Trail**: Complete trading history and decision logs
- **Kill Switch**: Manual override for all automated trading

## Development Prompt

### System Architecture Prompt
```
Create a comprehensive stock screener and automated trading application with the following architecture:

CORE COMPONENTS:
1. Real-time market data ingestion system supporting multiple data providers
2. Advanced screening engine with technical and fundamental analysis
3. Rule-based automation engine with risk management
4. Multi-broker API integration (ThinkOrSwim, Interactive Brokers)
5. Options trading strategy builder and execution engine
6. Comprehensive backtesting and performance analytics
7. Web-based dashboard and mobile applications

TECHNICAL STACK:
- Backend: Python/FastAPI with asyncio for concurrent processing
- Database: PostgreSQL for relational data, InfluxDB for time-series
- Real-time: WebSocket connections with Redis for caching
- Frontend: React.js with real-time charting libraries
- Mobile: React Native for cross-platform compatibility
- Cloud: AWS with auto-scaling and load balancing
- Security: OAuth 2.0, JWT tokens, encrypted API key storage

SPECIFIC FEATURES TO IMPLEMENT:
1. Screening engine that can process 8000+ stocks in real-time
2. Automated order execution with pre-trade risk checks
3. Options strategy builder supporting 20+ common strategies
4. Portfolio rebalancing with tax-loss harvesting
5. Machine learning models for entry/exit timing optimization
6. Comprehensive alerting system (email, SMS, push notifications)
7. Paper trading mode for strategy testing
8. Integration with popular charting platforms

COMPLIANCE REQUIREMENTS:
- SEC automated trading regulations compliance
- Broker-specific API rate limits and requirements
- Complete audit trail for all trading decisions
- Risk management circuit breakers
- User consent and disclosure management

Build this as a scalable, enterprise-grade application that can handle thousands of concurrent users and execute hundreds of trades per second while maintaining strict security and compliance standards.
```

### Frontend Development Prompt
```
Develop a sophisticated web application frontend for a stock screener and automated trading platform:

USER INTERFACE REQUIREMENTS:
1. Real-time dashboard with portfolio performance metrics
2. Advanced screening interface with drag-and-drop criteria builder
3. Strategy creation wizard with visual workflow builder
4. Comprehensive backtesting suite with interactive charts
5. Options strategy builder with risk/reward visualization
6. Trade execution interface with one-click automation toggle
7. Risk management dashboard with real-time monitoring
8. Mobile-responsive design for all components

TECHNICAL SPECIFICATIONS:
- React.js with TypeScript for type safety
- Real-time data updates using WebSocket connections
- TradingView charts integration for advanced charting
- Material-UI or Ant Design for consistent UI components
- Redux or Zustand for state management
- React Query for efficient data fetching and caching
- Progressive Web App (PWA) capabilities

KEY FEATURES:
1. Customizable dashboard with draggable widgets
2. Real-time portfolio tracking with P&L visualization
3. Strategy builder with conditional logic interface
4. Backtesting results with detailed performance metrics
5. Risk management tools with visual risk indicators
6. Alert management system with custom notifications
7. Trade history and audit trail visualization
8. Multi-account management interface

DESIGN PRINCIPLES:
- Dark theme optimized for extended use
- Accessibility compliance (WCAG 2.1 AA)
- Mobile-first responsive design
- Performance optimization for real-time data
- Intuitive user experience for both beginners and professionals
- Comprehensive error handling and user feedback
```

### Backend API Development Prompt
```
Build a robust backend API system for a stock screener and automated trading platform:

CORE FUNCTIONALITY:
1. Real-time market data processing and distribution
2. Advanced stock screening with custom criteria support
3. Automated trade execution with multiple broker integration
4. Options trading strategy management and execution
5. Portfolio management with real-time position tracking
6. Risk management with automated circuit breakers
7. Backtesting engine with historical data analysis
8. User authentication and authorization system

TECHNICAL ARCHITECTURE:
- Python with FastAPI framework for high-performance async API
- PostgreSQL for user data and trading history
- InfluxDB for time-series market data storage
- Redis for caching and real-time data distribution
- Celery for background task processing
- WebSocket support for real-time client updates
- Docker containerization for scalable deployment

BROKER INTEGRATIONS:
1. ThinkOrSwim (TD Ameritrade) API integration
2. Interactive Brokers TWS API integration
3. Generic REST API framework for additional brokers
4. Order management system with error handling
5. Real-time position and balance synchronization
6. Options chain data retrieval and processing

SCREENING ENGINE:
- Support for 50+ technical indicators
- Fundamental analysis metrics integration
- Custom formula builder with mathematical expressions
- Real-time scanning of 8000+ stocks
- Historical backtesting capabilities
- Performance optimization for sub-second results

RISK MANAGEMENT:
- Position sizing algorithms (Kelly, fixed fractional)
- Real-time portfolio risk monitoring
- Automated stop-loss and take-profit execution
- Correlation analysis and concentration limits
- VaR calculations and stress testing
- Emergency trading halt mechanisms

SECURITY & COMPLIANCE:
- OAuth 2.0 authentication with JWT tokens
- Encrypted storage of API keys and sensitive data
- Complete audit trail for all trading activities
- Rate limiting and DDoS protection
- SEC compliance monitoring and reporting
- Data encryption in transit and at rest

Build this as a production-ready system capable of handling high-frequency trading scenarios while maintaining strict security and regulatory compliance.
```

My github is https://github.com/HectorTa1989. Show me github readme with some good product names that nobody registered website domain with those names before, system architecture in mermaid syntax, workflow in mermaid syntax, Project structure all in github readme. Then code for each file in the project structure in separate artifacts (each file in 1 block) with exact file path, file name. Write commit message for each file after each file, so I can commit to github. Code using our own algorithms and free APIs is better