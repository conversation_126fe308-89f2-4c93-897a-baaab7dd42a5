import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Box, CircularProgress, Typography } from '@mui/material';
import { RootState } from '../../store/store';
import { checkAuthStatus } from '../../store/slices/authSlice';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredSubscription?: 'free' | 'premium' | 'enterprise';
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredSubscription = 'free',
}) => {
  const dispatch = useDispatch();
  const location = useLocation();
  
  const {
    isAuthenticated,
    isLoading,
    user,
    token,
  } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Check authentication status on mount if we have a token but no user
    if (token && !user && !isLoading) {
      dispatch(checkAuthStatus() as any);
    }
  }, [dispatch, token, user, isLoading]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: 'background.default',
        }}
      >
        <CircularProgress size={60} sx={{ mb: 2 }} />
        <Typography variant="h6" color="text.secondary">
          Loading...
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return (
      <Navigate
        to="/login"
        state={{ from: location }}
        replace
      />
    );
  }

  // Check subscription tier requirements
  const subscriptionHierarchy = {
    free: 0,
    premium: 1,
    enterprise: 2,
  };

  const userTierLevel = subscriptionHierarchy[user.subscription_tier as keyof typeof subscriptionHierarchy] || 0;
  const requiredTierLevel = subscriptionHierarchy[requiredSubscription];

  if (userTierLevel < requiredTierLevel) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          backgroundColor: 'background.default',
          p: 3,
        }}
      >
        <Typography variant="h4" color="error" gutterBottom>
          Access Denied
        </Typography>
        <Typography variant="body1" color="text.secondary" textAlign="center">
          This feature requires a {requiredSubscription} subscription or higher.
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Your current plan: {user.subscription_tier}
        </Typography>
      </Box>
    );
  }

  // Render protected content
  return <>{children}</>;
};

export default ProtectedRoute;
