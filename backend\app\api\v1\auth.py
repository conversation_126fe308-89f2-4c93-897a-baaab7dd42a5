"""
Authentication API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer
from pydantic import BaseModel, EmailStr
from sqlalchemy import select
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from app.core.database import get_db
from app.core.security import <PERSON><PERSON><PERSON><PERSON>, get_current_user, get_current_active_user
from app.models.user import User, UserSession
from app.core.config import settings

router = APIRouter()
security = HTTPBearer()

# Pydantic models for request/response
class UserRegister(BaseModel):
    email: EmailStr
    username: str
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int

class UserResponse(BaseModel):
    id: int
    email: str
    username: str
    first_name: Optional[str]
    last_name: Optional[str]
    is_active: bool
    is_verified: bool
    subscription_tier: str
    created_at: str

class PasswordChange(BaseModel):
    current_password: str
    new_password: str

class PasswordReset(BaseModel):
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(user_data: UserRegister):
    """Register a new user"""
    async with get_db() as db:
        # Check if user already exists
        existing_user = await db.execute(
            select(User).where(
                (User.email == user_data.email) | (User.username == user_data.username)
            )
        )
        if existing_user.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email or username already exists"
            )
        
        # Create new user
        hashed_password = SecurityManager.get_password_hash(user_data.password)
        
        new_user = User(
            email=user_data.email,
            username=user_data.username,
            hashed_password=hashed_password,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            is_active=True,
            is_verified=False,  # Would require email verification in production
            subscription_tier="free"
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        return UserResponse(
            id=new_user.id,
            email=new_user.email,
            username=new_user.username,
            first_name=new_user.first_name,
            last_name=new_user.last_name,
            is_active=new_user.is_active,
            is_verified=new_user.is_verified,
            subscription_tier=new_user.subscription_tier,
            created_at=new_user.created_at.isoformat()
        )

@router.post("/login", response_model=Token)
async def login_user(user_credentials: UserLogin):
    """Login user and return access token"""
    async with get_db() as db:
        # Find user by email
        result = await db.execute(
            select(User).where(User.email == user_credentials.email)
        )
        user = result.scalar_one_or_none()
        
        if not user or not SecurityManager.verify_password(
            user_credentials.password, user.hashed_password
        ):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user account"
            )
        
        # Create access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = SecurityManager.create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )
        
        # Create refresh token
        refresh_token = SecurityManager.create_refresh_token(user.id)
        
        # Create user session
        session = UserSession(
            user_id=user.id,
            session_token=access_token,
            refresh_token=refresh_token,
            expires_at=datetime.utcnow() + access_token_expires,
            ip_address="127.0.0.1",  # Would get from request in production
            user_agent="MarketHawk Client"  # Would get from request headers
        )
        
        db.add(session)
        
        # Update last login
        user.last_login = datetime.utcnow()
        
        await db.commit()
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )

@router.post("/logout")
async def logout_user(current_user: User = Depends(get_current_active_user)):
    """Logout user and invalidate token"""
    # In a full implementation, we would blacklist the token
    # For now, we'll just return success
    return {"message": "Successfully logged out"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """Get current user information"""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        username=current_user.username,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        subscription_tier=current_user.subscription_tier,
        created_at=current_user.created_at.isoformat()
    )

@router.put("/me", response_model=UserResponse)
async def update_user_profile(
    user_update: UserRegister,
    current_user: User = Depends(get_current_active_user)
):
    """Update user profile"""
    async with get_db() as db:
        # Check if email/username is already taken by another user
        if user_update.email != current_user.email:
            existing_user = await db.execute(
                select(User).where(
                    (User.email == user_update.email) & (User.id != current_user.id)
                )
            )
            if existing_user.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already taken"
                )
        
        if user_update.username != current_user.username:
            existing_user = await db.execute(
                select(User).where(
                    (User.username == user_update.username) & (User.id != current_user.id)
                )
            )
            if existing_user.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken"
                )
        
        # Update user fields
        current_user.email = user_update.email
        current_user.username = user_update.username
        current_user.first_name = user_update.first_name
        current_user.last_name = user_update.last_name
        
        if user_update.password:
            current_user.hashed_password = SecurityManager.get_password_hash(
                user_update.password
            )
        
        await db.commit()
        await db.refresh(current_user)
        
        return UserResponse(
            id=current_user.id,
            email=current_user.email,
            username=current_user.username,
            first_name=current_user.first_name,
            last_name=current_user.last_name,
            is_active=current_user.is_active,
            is_verified=current_user.is_verified,
            subscription_tier=current_user.subscription_tier,
            created_at=current_user.created_at.isoformat()
        )

@router.post("/change-password")
async def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_active_user)
):
    """Change user password"""
    async with get_db() as db:
        # Verify current password
        if not SecurityManager.verify_password(
            password_data.current_password, current_user.hashed_password
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )
        
        # Update password
        current_user.hashed_password = SecurityManager.get_password_hash(
            password_data.new_password
        )
        
        await db.commit()
        
        return {"message": "Password changed successfully"}

@router.post("/generate-api-key")
async def generate_api_key(current_user: User = Depends(get_current_active_user)):
    """Generate new API key for user"""
    async with get_db() as db:
        # Generate new API key
        api_key = SecurityManager.generate_api_key()
        api_key_hash = SecurityManager.hash_api_key(api_key)
        
        # Update user record
        current_user.api_key_hash = api_key_hash
        current_user.api_key_created_at = datetime.utcnow()
        
        await db.commit()
        
        return {
            "api_key": api_key,
            "message": "API key generated successfully. Store it securely as it won't be shown again."
        }

@router.delete("/revoke-api-key")
async def revoke_api_key(current_user: User = Depends(get_current_active_user)):
    """Revoke user's API key"""
    async with get_db() as db:
        current_user.api_key_hash = None
        current_user.api_key_created_at = None
        
        await db.commit()
        
        return {"message": "API key revoked successfully"}

@router.get("/sessions")
async def get_user_sessions(current_user: User = Depends(get_current_active_user)):
    """Get user's active sessions"""
    async with get_db() as db:
        result = await db.execute(
            select(UserSession)
            .where(UserSession.user_id == current_user.id)
            .where(UserSession.is_active == True)
            .order_by(UserSession.created_at.desc())
        )
        sessions = result.scalars().all()
        
        return [session.to_dict() for session in sessions]

@router.delete("/sessions/{session_id}")
async def revoke_session(
    session_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Revoke a specific session"""
    async with get_db() as db:
        result = await db.execute(
            select(UserSession)
            .where(UserSession.id == session_id)
            .where(UserSession.user_id == current_user.id)
        )
        session = result.scalar_one_or_none()
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        session.is_active = False
        await db.commit()
        
        # Blacklist the token
        await SecurityManager.blacklist_token(session.session_token)
        
        return {"message": "Session revoked successfully"}
