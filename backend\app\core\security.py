"""
Security utilities for authentication and authorization
"""

from datetime import datetime, timedelta
from typing import Optional, Union, Any
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import secrets
import hashlib
import hmac
from cryptography.fernet import <PERSON><PERSON>t
import logging

from app.core.config import settings
from app.core.database import get_db, get_redis
from app.models.user import User

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Security scheme
security = HTTPBearer()

# Encryption for sensitive data (API keys, etc.)
encryption_key = Fernet.generate_key()
cipher_suite = Fernet(encryption_key)

class SecurityManager:
    """Security management utilities"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """Generate password hash"""
        return pwd_context.hash(password)
    
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(user_id: int) -> str:
        """Create refresh token"""
        data = {
            "sub": str(user_id),
            "type": "refresh",
            "exp": datetime.utcnow() + timedelta(days=30)
        }
        return jwt.encode(data, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    
    @staticmethod
    def verify_token(token: str) -> Optional[dict]:
        """Verify JWT token and return payload"""
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            return payload
        except JWTError as e:
            logger.warning(f"Token verification failed: {e}")
            return None
    
    @staticmethod
    def generate_api_key() -> str:
        """Generate secure API key"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def hash_api_key(api_key: str) -> str:
        """Hash API key for storage"""
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    @staticmethod
    def verify_api_key(api_key: str, hashed_key: str) -> bool:
        """Verify API key against hash"""
        return hmac.compare_digest(
            hashlib.sha256(api_key.encode()).hexdigest(),
            hashed_key
        )
    
    @staticmethod
    def encrypt_sensitive_data(data: str) -> str:
        """Encrypt sensitive data like broker API keys"""
        return cipher_suite.encrypt(data.encode()).decode()
    
    @staticmethod
    def decrypt_sensitive_data(encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    @staticmethod
    async def blacklist_token(token: str, expires_in: int = None):
        """Add token to blacklist"""
        try:
            redis_client = await get_redis()
            if expires_in is None:
                expires_in = settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            
            await redis_client.setex(f"blacklist:{token}", expires_in, "1")
        except Exception as e:
            logger.error(f"Error blacklisting token: {e}")
    
    @staticmethod
    async def is_token_blacklisted(token: str) -> bool:
        """Check if token is blacklisted"""
        try:
            redis_client = await get_redis()
            result = await redis_client.get(f"blacklist:{token}")
            return result is not None
        except Exception as e:
            logger.error(f"Error checking token blacklist: {e}")
            return False

# Authentication dependencies
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """Get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        token = credentials.credentials
        
        # Check if token is blacklisted
        if await SecurityManager.is_token_blacklisted(token):
            raise credentials_exception
        
        # Verify token
        payload = SecurityManager.verify_token(token)
        if payload is None:
            raise credentials_exception
        
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
        
        # Get user from database
        async with get_db() as db:
            user = await db.get(User, int(user_id))
            if user is None:
                raise credentials_exception
            
            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Inactive user"
                )
            
            return user
            
    except JWTError:
        raise credentials_exception
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise credentials_exception

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

def require_subscription_tier(required_tier: str):
    """Decorator to require specific subscription tier"""
    def decorator(current_user: User = Depends(get_current_active_user)):
        subscription_hierarchy = {
            'free': 0,
            'premium': 1,
            'enterprise': 2
        }

        user_tier_level = subscription_hierarchy.get(current_user.subscription_tier, 0)
        required_tier_level = subscription_hierarchy.get(required_tier, 0)

        if user_tier_level < required_tier_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"This feature requires {required_tier} subscription or higher"
            )

        return current_user

    return decorator

async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get current admin user"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

# Rate limiting
class RateLimiter:
    """Rate limiting utilities"""
    
    @staticmethod
    async def check_rate_limit(
        key: str,
        limit: int = None,
        window: int = 60
    ) -> bool:
        """Check if request is within rate limit"""
        if limit is None:
            limit = settings.RATE_LIMIT_PER_MINUTE
        
        try:
            redis_client = await get_redis()
            current = await redis_client.get(f"rate_limit:{key}")
            
            if current is None:
                await redis_client.setex(f"rate_limit:{key}", window, 1)
                return True
            
            if int(current) >= limit:
                return False
            
            await redis_client.incr(f"rate_limit:{key}")
            return True
            
        except Exception as e:
            logger.error(f"Rate limiting error: {e}")
            return True  # Allow request if rate limiting fails
    
    @staticmethod
    async def get_rate_limit_info(key: str) -> dict:
        """Get rate limit information"""
        try:
            redis_client = await get_redis()
            current = await redis_client.get(f"rate_limit:{key}")
            ttl = await redis_client.ttl(f"rate_limit:{key}")
            
            return {
                "current": int(current) if current else 0,
                "limit": settings.RATE_LIMIT_PER_MINUTE,
                "reset_in": ttl if ttl > 0 else 0
            }
        except Exception as e:
            logger.error(f"Error getting rate limit info: {e}")
            return {
                "current": 0,
                "limit": settings.RATE_LIMIT_PER_MINUTE,
                "reset_in": 0
            }

# API Key authentication
async def verify_api_key(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """Verify API key authentication"""
    api_key_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid API key",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        api_key = credentials.credentials
        
        # Check rate limit for API key
        if not await RateLimiter.check_rate_limit(f"api_key:{api_key}"):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="Rate limit exceeded"
            )
        
        # Find user by API key hash
        hashed_key = SecurityManager.hash_api_key(api_key)
        
        async with get_db() as db:
            # This would need to be implemented in the User model
            # user = await db.query(User).filter(User.api_key_hash == hashed_key).first()
            # For now, we'll use a placeholder
            user = None
            
            if user is None or not user.is_active:
                raise api_key_exception
            
            return user
            
    except Exception as e:
        logger.error(f"API key verification error: {e}")
        raise api_key_exception

# Security headers middleware
def add_security_headers(response):
    """Add security headers to response"""
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    return response

# Input validation
class InputValidator:
    """Input validation utilities"""
    
    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """Validate stock symbol format"""
        if not symbol or len(symbol) > 10:
            return False
        return symbol.isalpha() and symbol.isupper()
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Basic email validation"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """Sanitize user input"""
        if not input_str:
            return ""
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '|', '`']
        for char in dangerous_chars:
            input_str = input_str.replace(char, '')
        
        return input_str.strip()
