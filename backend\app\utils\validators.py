"""
Validation utilities for API inputs and business logic
Provides comprehensive validation for trading and screening operations
"""

import re
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from pydantic import ValidationError
from fastapi import HTTPException, status


def validate_symbol(symbol: str) -> str:
    """
    Validate stock symbol format
    
    Args:
        symbol: Stock symbol to validate
        
    Returns:
        Normalized symbol (uppercase)
        
    Raises:
        ValueError: If symbol format is invalid
    """
    if not symbol or not isinstance(symbol, str):
        raise ValueError("Symbol must be a non-empty string")
    
    # Remove whitespace and convert to uppercase
    symbol = symbol.strip().upper()
    
    # Basic symbol format validation
    if not re.match(r'^[A-Z]{1,5}$', symbol):
        raise ValueError("Symbol must be 1-5 uppercase letters")
    
    # Check for common invalid symbols
    invalid_symbols = {'NULL', 'NONE', 'TEST', 'DEMO'}
    if symbol in invalid_symbols:
        raise ValueError(f"Invalid symbol: {symbol}")
    
    return symbol


def validate_symbols_list(symbols: List[str], max_symbols: int = 1000) -> List[str]:
    """
    Validate list of stock symbols
    
    Args:
        symbols: List of symbols to validate
        max_symbols: Maximum number of symbols allowed
        
    Returns:
        List of normalized symbols
        
    Raises:
        ValueError: If validation fails
    """
    if not symbols:
        raise ValueError("Symbols list cannot be empty")
    
    if len(symbols) > max_symbols:
        raise ValueError(f"Too many symbols. Maximum allowed: {max_symbols}")
    
    validated_symbols = []
    seen_symbols = set()
    
    for symbol in symbols:
        try:
            normalized_symbol = validate_symbol(symbol)
            
            # Check for duplicates
            if normalized_symbol in seen_symbols:
                continue  # Skip duplicates
            
            seen_symbols.add(normalized_symbol)
            validated_symbols.append(normalized_symbol)
            
        except ValueError as e:
            raise ValueError(f"Invalid symbol '{symbol}': {str(e)}")
    
    return validated_symbols


def validate_screening_criteria(criteria: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Validate screening criteria for strategy creation
    
    Args:
        criteria: List of screening criteria dictionaries
        
    Returns:
        Validated criteria list
        
    Raises:
        ValueError: If validation fails
    """
    if not criteria:
        raise ValueError("Screening criteria cannot be empty")
    
    if len(criteria) > 20:
        raise ValueError("Maximum 20 screening criteria allowed")
    
    # Valid fields for screening
    valid_fields = {
        'price', 'volume', 'market_cap', 'pe_ratio', 'pb_ratio', 'debt_to_equity',
        'roe', 'rsi', 'macd', 'sma_20', 'sma_50', 'sma_200', 'bollinger_upper',
        'bollinger_lower', 'atr', 'beta', 'dividend_yield', 'eps_growth',
        'revenue_growth', 'free_cash_flow', 'analyst_rating'
    }
    
    # Valid operators
    valid_operators = {'>', '<', '>=', '<=', '==', '!=', 'between', 'in'}
    
    validated_criteria = []
    
    for i, criterion in enumerate(criteria):
        if not isinstance(criterion, dict):
            raise ValueError(f"Criterion {i+1} must be a dictionary")
        
        # Validate required fields
        required_fields = ['field', 'operator', 'value', 'weight']
        for field in required_fields:
            if field not in criterion:
                raise ValueError(f"Criterion {i+1} missing required field: {field}")
        
        # Validate field
        field = criterion['field']
        if field not in valid_fields:
            raise ValueError(f"Criterion {i+1}: Invalid field '{field}'. Valid fields: {sorted(valid_fields)}")
        
        # Validate operator
        operator = criterion['operator']
        if operator not in valid_operators:
            raise ValueError(f"Criterion {i+1}: Invalid operator '{operator}'. Valid operators: {sorted(valid_operators)}")
        
        # Validate value
        value = criterion['value']
        if operator in ['between']:
            if not isinstance(value, list) or len(value) != 2:
                raise ValueError(f"Criterion {i+1}: 'between' operator requires list of 2 values")
            if not all(isinstance(v, (int, float)) for v in value):
                raise ValueError(f"Criterion {i+1}: 'between' values must be numbers")
            if value[0] >= value[1]:
                raise ValueError(f"Criterion {i+1}: 'between' first value must be less than second")
        elif operator in ['in']:
            if not isinstance(value, list):
                raise ValueError(f"Criterion {i+1}: 'in' operator requires list of values")
            if len(value) > 10:
                raise ValueError(f"Criterion {i+1}: 'in' operator supports maximum 10 values")
        else:
            if not isinstance(value, (int, float)):
                raise ValueError(f"Criterion {i+1}: Value must be a number")
        
        # Validate weight
        weight = criterion['weight']
        if not isinstance(weight, (int, float)) or weight <= 0 or weight > 10:
            raise ValueError(f"Criterion {i+1}: Weight must be between 0.1 and 10.0")
        
        # Field-specific validations
        if field == 'rsi' and isinstance(value, (int, float)):
            if not (0 <= value <= 100):
                raise ValueError(f"Criterion {i+1}: RSI value must be between 0 and 100")
        
        if field in ['pe_ratio', 'pb_ratio'] and isinstance(value, (int, float)):
            if value < 0:
                raise ValueError(f"Criterion {i+1}: {field} cannot be negative")
        
        if field == 'volume' and isinstance(value, (int, float)):
            if value < 0:
                raise ValueError(f"Criterion {i+1}: Volume cannot be negative")
        
        validated_criteria.append(criterion)
    
    return validated_criteria


def validate_date_range(start_date: datetime, end_date: datetime, max_days: int = 1825) -> tuple:
    """
    Validate date range for backtesting and analysis
    
    Args:
        start_date: Start date
        end_date: End date
        max_days: Maximum number of days allowed in range
        
    Returns:
        Tuple of (start_date, end_date)
        
    Raises:
        ValueError: If validation fails
    """
    if not isinstance(start_date, datetime) or not isinstance(end_date, datetime):
        raise ValueError("Dates must be datetime objects")
    
    if start_date >= end_date:
        raise ValueError("Start date must be before end date")
    
    # Check if dates are in the future
    now = datetime.utcnow()
    if start_date > now:
        raise ValueError("Start date cannot be in the future")
    
    if end_date > now:
        end_date = now  # Adjust end date to current time
    
    # Check date range length
    days_diff = (end_date - start_date).days
    if days_diff > max_days:
        raise ValueError(f"Date range too large. Maximum {max_days} days allowed")
    
    if days_diff < 1:
        raise ValueError("Date range must be at least 1 day")
    
    # Check minimum start date (e.g., no data before 2000)
    min_date = datetime(2000, 1, 1)
    if start_date < min_date:
        raise ValueError(f"Start date cannot be before {min_date.strftime('%Y-%m-%d')}")
    
    return start_date, end_date


def validate_portfolio_allocation(allocations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Validate portfolio allocation targets
    
    Args:
        allocations: List of allocation dictionaries
        
    Returns:
        Validated allocations
        
    Raises:
        ValueError: If validation fails
    """
    if not allocations:
        raise ValueError("Allocations cannot be empty")
    
    if len(allocations) > 50:
        raise ValueError("Maximum 50 allocations allowed")
    
    total_percentage = 0
    seen_symbols = set()
    validated_allocations = []
    
    for i, allocation in enumerate(allocations):
        if not isinstance(allocation, dict):
            raise ValueError(f"Allocation {i+1} must be a dictionary")
        
        # Validate required fields
        if 'symbol' not in allocation or 'target_percentage' not in allocation:
            raise ValueError(f"Allocation {i+1} missing required fields: symbol, target_percentage")
        
        # Validate symbol
        try:
            symbol = validate_symbol(allocation['symbol'])
        except ValueError as e:
            raise ValueError(f"Allocation {i+1}: {str(e)}")
        
        # Check for duplicate symbols
        if symbol in seen_symbols:
            raise ValueError(f"Allocation {i+1}: Duplicate symbol '{symbol}'")
        seen_symbols.add(symbol)
        
        # Validate target percentage
        target_percentage = allocation['target_percentage']
        if not isinstance(target_percentage, (int, float)):
            raise ValueError(f"Allocation {i+1}: Target percentage must be a number")
        
        if not (0 <= target_percentage <= 100):
            raise ValueError(f"Allocation {i+1}: Target percentage must be between 0 and 100")
        
        total_percentage += target_percentage
        
        # Validate optional min/max percentages
        min_percentage = allocation.get('min_percentage')
        max_percentage = allocation.get('max_percentage')
        
        if min_percentage is not None:
            if not isinstance(min_percentage, (int, float)) or not (0 <= min_percentage <= 100):
                raise ValueError(f"Allocation {i+1}: Min percentage must be between 0 and 100")
            
            if min_percentage > target_percentage:
                raise ValueError(f"Allocation {i+1}: Min percentage cannot exceed target percentage")
        
        if max_percentage is not None:
            if not isinstance(max_percentage, (int, float)) or not (0 <= max_percentage <= 100):
                raise ValueError(f"Allocation {i+1}: Max percentage must be between 0 and 100")
            
            if max_percentage < target_percentage:
                raise ValueError(f"Allocation {i+1}: Max percentage cannot be less than target percentage")
        
        if min_percentage is not None and max_percentage is not None:
            if min_percentage > max_percentage:
                raise ValueError(f"Allocation {i+1}: Min percentage cannot exceed max percentage")
        
        validated_allocations.append({
            'symbol': symbol,
            'target_percentage': target_percentage,
            'min_percentage': min_percentage,
            'max_percentage': max_percentage
        })
    
    # Check total percentage
    if abs(total_percentage - 100.0) > 0.01:  # Allow small floating point errors
        raise ValueError(f"Total allocation percentage must equal 100%, got {total_percentage}%")
    
    return validated_allocations


def validate_order_parameters(
    symbol: str,
    quantity: int,
    price: Optional[float] = None,
    order_type: str = "market"
) -> Dict[str, Any]:
    """
    Validate order parameters
    
    Args:
        symbol: Stock symbol
        quantity: Order quantity
        price: Order price (for limit orders)
        order_type: Order type
        
    Returns:
        Validated parameters
        
    Raises:
        ValueError: If validation fails
    """
    # Validate symbol
    validated_symbol = validate_symbol(symbol)
    
    # Validate quantity
    if not isinstance(quantity, int) or quantity <= 0:
        raise ValueError("Quantity must be a positive integer")
    
    if quantity > 10000:
        raise ValueError("Quantity cannot exceed 10,000 shares")
    
    # Validate order type
    valid_order_types = ['market', 'limit', 'stop', 'stop_limit']
    if order_type not in valid_order_types:
        raise ValueError(f"Invalid order type. Valid types: {valid_order_types}")
    
    # Validate price for limit orders
    if order_type in ['limit', 'stop_limit']:
        if price is None:
            raise ValueError(f"Price is required for {order_type} orders")
        
        if not isinstance(price, (int, float)) or price <= 0:
            raise ValueError("Price must be a positive number")
        
        if price > 100000:
            raise ValueError("Price cannot exceed $100,000")
    
    return {
        'symbol': validated_symbol,
        'quantity': quantity,
        'price': price,
        'order_type': order_type
    }


def validate_risk_parameters(
    max_position_size: float,
    max_daily_loss: float,
    max_total_risk: float
) -> Dict[str, float]:
    """
    Validate risk management parameters
    
    Args:
        max_position_size: Maximum position size as percentage
        max_daily_loss: Maximum daily loss as percentage
        max_total_risk: Maximum total portfolio risk as percentage
        
    Returns:
        Validated parameters
        
    Raises:
        ValueError: If validation fails
    """
    # Validate max position size
    if not isinstance(max_position_size, (int, float)):
        raise ValueError("Max position size must be a number")
    
    if not (0.01 <= max_position_size <= 1.0):
        raise ValueError("Max position size must be between 1% and 100%")
    
    # Validate max daily loss
    if not isinstance(max_daily_loss, (int, float)):
        raise ValueError("Max daily loss must be a number")
    
    if not (0.01 <= max_daily_loss <= 1.0):
        raise ValueError("Max daily loss must be between 1% and 100%")
    
    # Validate max total risk
    if not isinstance(max_total_risk, (int, float)):
        raise ValueError("Max total risk must be a number")
    
    if not (0.01 <= max_total_risk <= 1.0):
        raise ValueError("Max total risk must be between 1% and 100%")
    
    # Logical validations
    if max_daily_loss > max_total_risk:
        raise ValueError("Max daily loss cannot exceed max total risk")
    
    return {
        'max_position_size': max_position_size,
        'max_daily_loss': max_daily_loss,
        'max_total_risk': max_total_risk
    }
