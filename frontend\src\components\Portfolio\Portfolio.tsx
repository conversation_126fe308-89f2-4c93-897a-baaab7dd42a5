import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Chip,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Add,
  Edit,
  Delete,
  Refresh,
  Assessment,
  PieChart,
  Timeline,
  AccountBalance,
  ShowChart,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { RootState } from '../../store/store';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`portfolio-tabpanel-${index}`}
      aria-labelledby={`portfolio-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

interface Portfolio {
  id: string;
  name: string;
  description: string;
  totalValue: number;
  totalReturn: number;
  totalReturnPercent: number;
  dayChange: number;
  dayChangePercent: number;
  positions: Position[];
  riskLevel: 'low' | 'medium' | 'high';
  createdAt: string;
}

interface Position {
  symbol: string;
  name: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
  weight: number;
  sector: string;
}

const Portfolio: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [selectedPortfolio, setSelectedPortfolio] = useState<Portfolio | null>(null);
  const [portfolioDialogOpen, setPortfolioDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    fetchPortfolios();
  }, []);

  const fetchPortfolios = async () => {
    setIsLoading(true);
    try {
      // Mock data - in real app, fetch from API
      const mockPortfolios: Portfolio[] = [
        {
          id: '1',
          name: 'Growth Portfolio',
          description: 'High-growth technology stocks',
          totalValue: 125750.50,
          totalReturn: 15750.50,
          totalReturnPercent: 14.32,
          dayChange: 2340.25,
          dayChangePercent: 1.89,
          riskLevel: 'high',
          createdAt: new Date().toISOString(),
          positions: [
            {
              symbol: 'AAPL',
              name: 'Apple Inc.',
              quantity: 100,
              avgPrice: 148.50,
              currentPrice: 152.25,
              marketValue: 15225.00,
              unrealizedPnL: 375.00,
              unrealizedPnLPercent: 2.52,
              weight: 12.1,
              sector: 'Technology',
            },
            {
              symbol: 'GOOGL',
              name: 'Alphabet Inc.',
              quantity: 10,
              avgPrice: 2750.00,
              currentPrice: 2785.50,
              marketValue: 27855.00,
              unrealizedPnL: 355.00,
              unrealizedPnLPercent: 1.29,
              weight: 22.1,
              sector: 'Technology',
            },
          ],
        },
        {
          id: '2',
          name: 'Dividend Portfolio',
          description: 'Stable dividend-paying stocks',
          totalValue: 85420.75,
          totalReturn: 8420.75,
          totalReturnPercent: 10.94,
          dayChange: -125.50,
          dayChangePercent: -0.15,
          riskLevel: 'low',
          createdAt: new Date().toISOString(),
          positions: [],
        },
      ];

      setPortfolios(mockPortfolios);
      if (mockPortfolios.length > 0) {
        setSelectedPortfolio(mockPortfolios[0]);
      }
    } catch (error) {
      console.error('Failed to fetch portfolios:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleCreatePortfolio = () => {
    setPortfolioDialogOpen(true);
  };

  const handleClosePortfolioDialog = () => {
    setPortfolioDialogOpen(false);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const totalPortfolioValue = portfolios.reduce((sum, p) => sum + p.totalValue, 0);
  const totalReturn = portfolios.reduce((sum, p) => sum + p.totalReturn, 0);
  const totalReturnPercent = totalPortfolioValue > 0 ? (totalReturn / (totalPortfolioValue - totalReturn)) * 100 : 0;

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1" fontWeight={600}>
            Portfolio Management
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Tooltip title="Refresh Data">
              <IconButton color="primary" onClick={fetchPortfolios}>
                <Refresh />
              </IconButton>
            </Tooltip>

            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={handleCreatePortfolio}
            >
              Create Portfolio
            </Button>
          </Box>
        </Box>

        <Typography variant="body1" color="text.secondary">
          Track and manage your investment portfolios
        </Typography>
      </Box>

      {/* Portfolio Summary */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Portfolio Value
                  </Typography>
                  <Typography variant="h5" fontWeight={600}>
                    {formatCurrency(totalPortfolioValue)}
                  </Typography>
                </Box>
                <AccountBalance color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Return
                  </Typography>
                  <Typography variant="h5" fontWeight={600}>
                    {formatCurrency(totalReturn)}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {totalReturn >= 0 ? (
                      <TrendingUp color="success" fontSize="small" />
                    ) : (
                      <TrendingDown color="error" fontSize="small" />
                    )}
                    <Typography
                      variant="body2"
                      sx={{
                        color: totalReturn >= 0 ? 'success.main' : 'error.main',
                        ml: 0.5,
                      }}
                    >
                      {formatPercent(totalReturnPercent)}
                    </Typography>
                  </Box>
                </Box>
                <ShowChart color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Portfolios
                  </Typography>
                  <Typography variant="h5" fontWeight={600}>
                    {portfolios.length}
                  </Typography>
                </Box>
                <PieChart color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Portfolio List */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {portfolios.map((portfolio, index) => (
          <Grid item xs={12} md={6} key={portfolio.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card
                sx={{
                  cursor: 'pointer',
                  border: selectedPortfolio?.id === portfolio.id ? 2 : 1,
                  borderColor: selectedPortfolio?.id === portfolio.id ? 'primary.main' : 'divider',
                  '&:hover': {
                    boxShadow: (theme) => theme.shadows[4],
                  },
                }}
                onClick={() => setSelectedPortfolio(portfolio)}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box>
                      <Typography variant="h6" fontWeight={600}>
                        {portfolio.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {portfolio.description}
                      </Typography>
                    </Box>
                    <Chip
                      label={portfolio.riskLevel.toUpperCase()}
                      color={getRiskColor(portfolio.riskLevel)}
                      size="small"
                    />
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="h5" fontWeight={600}>
                      {formatCurrency(portfolio.totalValue)}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography
                        variant="body2"
                        sx={{ color: portfolio.totalReturn >= 0 ? 'success.main' : 'error.main' }}
                      >
                        {formatCurrency(portfolio.totalReturn)}
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{ color: portfolio.totalReturn >= 0 ? 'success.main' : 'error.main' }}
                      >
                        ({formatPercent(portfolio.totalReturnPercent)})
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      {portfolio.positions.length} positions
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton size="small" color="primary">
                        <Edit />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <Delete />
                      </IconButton>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Portfolio Details */}
      {selectedPortfolio && (
        <Card>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="portfolio tabs">
              <Tab
                label="Holdings"
                icon={<AccountBalance />}
                iconPosition="start"
                id="portfolio-tab-0"
                aria-controls="portfolio-tabpanel-0"
              />
              <Tab
                label="Performance"
                icon={<Timeline />}
                iconPosition="start"
                id="portfolio-tab-1"
                aria-controls="portfolio-tabpanel-1"
              />
              <Tab
                label="Analytics"
                icon={<Assessment />}
                iconPosition="start"
                id="portfolio-tab-2"
                aria-controls="portfolio-tabpanel-2"
              />
            </Tabs>
          </Box>

          <TabPanel value={activeTab} index={0}>
            {/* Holdings Table */}
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Symbol</TableCell>
                    <TableCell>Company</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Avg Price</TableCell>
                    <TableCell align="right">Current Price</TableCell>
                    <TableCell align="right">Market Value</TableCell>
                    <TableCell align="right">Unrealized P&L</TableCell>
                    <TableCell align="right">Weight</TableCell>
                    <TableCell>Sector</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedPortfolio.positions.map((position, index) => (
                    <TableRow
                      key={position.symbol}
                      component={motion.tr}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                    >
                      <TableCell>
                        <Typography variant="body2" fontWeight={600}>
                          {position.symbol}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {position.name}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">{position.quantity}</TableCell>
                      <TableCell align="right">{formatCurrency(position.avgPrice)}</TableCell>
                      <TableCell align="right">{formatCurrency(position.currentPrice)}</TableCell>
                      <TableCell align="right">{formatCurrency(position.marketValue)}</TableCell>
                      <TableCell align="right">
                        <Box>
                          <Typography
                            variant="body2"
                            sx={{ color: position.unrealizedPnL >= 0 ? 'success.main' : 'error.main' }}
                          >
                            {formatCurrency(position.unrealizedPnL)}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{ color: position.unrealizedPnL >= 0 ? 'success.main' : 'error.main' }}
                          >
                            ({formatPercent(position.unrealizedPnLPercent)})
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={position.weight}
                            sx={{ width: 50, height: 6 }}
                          />
                          <Typography variant="caption">
                            {position.weight.toFixed(1)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip label={position.sector} size="small" variant="outlined" />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h6" color="text.secondary">
                Performance Charts
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                View detailed performance analytics and charts
              </Typography>
              <Button variant="outlined" sx={{ mt: 2 }}>
                Coming Soon
              </Button>
            </Box>
          </TabPanel>

          <TabPanel value={activeTab} index={2}>
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h6" color="text.secondary">
                Portfolio Analytics
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Risk metrics, correlation analysis, and optimization tools
              </Typography>
              <Button variant="outlined" sx={{ mt: 2 }}>
                Coming Soon
              </Button>
            </Box>
          </TabPanel>
        </Card>
      )}

      {/* Create Portfolio Dialog */}
      <Dialog open={portfolioDialogOpen} onClose={handleClosePortfolioDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Portfolio</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Portfolio Name"
                  placeholder="e.g., Growth Portfolio"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Description"
                  placeholder="Describe your portfolio strategy..."
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Risk Level</InputLabel>
                  <Select defaultValue="medium">
                    <MenuItem value="low">Low Risk</MenuItem>
                    <MenuItem value="medium">Medium Risk</MenuItem>
                    <MenuItem value="high">High Risk</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePortfolioDialog}>Cancel</Button>
          <Button variant="contained" onClick={handleClosePortfolioDialog}>
            Create Portfolio
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Portfolio;
