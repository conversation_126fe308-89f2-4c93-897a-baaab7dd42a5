"""
Risk Management Service
Provides comprehensive risk management for trading operations
"""

import asyncio
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import logging

from sqlalchemy import select, and_, func
from app.core.database import get_db
from app.models.user import User, BrokerAccount
from app.models.trade import Trade, Position, OrderStatus
from app.models.portfolio import Portfolio
from app.models.stock import Stock
from app.core.config import settings

logger = logging.getLogger(__name__)


class RiskLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskCheckResult:
    """Result of risk validation check"""
    is_valid: bool
    risk_level: RiskLevel
    reason: Optional[str] = None
    warnings: List[str] = None
    recommendations: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []
        if self.recommendations is None:
            self.recommendations = []


@dataclass
class PositionRisk:
    """Risk metrics for a position"""
    symbol: str
    position_value: float
    portfolio_percentage: float
    var_1_day: float  # Value at Risk
    beta: float
    correlation_risk: float
    concentration_risk: float


@dataclass
class PortfolioRisk:
    """Portfolio-level risk metrics"""
    total_value: float
    cash_percentage: float
    equity_percentage: float
    var_1_day: float
    var_5_day: float
    expected_shortfall: float
    portfolio_beta: float
    sharpe_ratio: float
    max_drawdown: float
    concentration_risk: Dict[str, float]  # Sector/stock concentration
    correlation_matrix: Dict[str, Dict[str, float]]


class RiskManager:
    """
    Comprehensive risk management system
    Handles position sizing, portfolio risk, and trade validation
    """
    
    def __init__(self):
        self.max_portfolio_risk = settings.MAX_PORTFOLIO_RISK
        self.max_sector_concentration = settings.MAX_SECTOR_CONCENTRATION
        self.max_position_size = settings.MAX_POSITION_SIZE
        self.max_daily_loss = settings.MAX_DAILY_LOSS
    
    async def validate_order(
        self,
        user: User,
        broker_account: BrokerAccount,
        order_request: Any
    ) -> RiskCheckResult:
        """
        Validate order against risk management rules
        
        Args:
            user: User placing the order
            broker_account: Broker account for the order
            order_request: Order request details
            
        Returns:
            RiskCheckResult with validation outcome
        """
        warnings = []
        recommendations = []
        
        try:
            async with get_db() as db:
                # Get current positions
                positions_result = await db.execute(
                    select(Position).where(
                        and_(
                            Position.user_id == user.id,
                            Position.broker_account_id == broker_account.id,
                            Position.quantity != 0
                        )
                    )
                )
                positions = positions_result.scalars().all()
                
                # Get stock information
                stock_result = await db.execute(
                    select(Stock).where(Stock.symbol == order_request.symbol.upper())
                )
                stock = stock_result.scalar_one_or_none()
                
                if not stock:
                    return RiskCheckResult(
                        is_valid=False,
                        risk_level=RiskLevel.HIGH,
                        reason=f"Stock {order_request.symbol} not found in database"
                    )
                
                # Calculate order value
                order_value = order_request.quantity * (order_request.price or stock.current_price or 0)
                
                # Check account balance
                if order_value > broker_account.buying_power:
                    return RiskCheckResult(
                        is_valid=False,
                        risk_level=RiskLevel.HIGH,
                        reason="Insufficient buying power for order"
                    )
                
                # Check position size limits
                portfolio_value = broker_account.total_equity
                position_percentage = (order_value / portfolio_value) * 100 if portfolio_value > 0 else 0
                
                max_position_limit = min(
                    user.default_position_size * 100,
                    self.max_position_size * 100
                )
                
                if position_percentage > max_position_limit:
                    return RiskCheckResult(
                        is_valid=False,
                        risk_level=RiskLevel.HIGH,
                        reason=f"Position size ({position_percentage:.1f}%) exceeds limit ({max_position_limit:.1f}%)"
                    )
                
                # Check sector concentration
                sector_exposure = await self._calculate_sector_exposure(
                    positions, stock, order_value, portfolio_value
                )
                
                if sector_exposure > self.max_sector_concentration * 100:
                    warnings.append(
                        f"High sector concentration: {sector_exposure:.1f}% in {stock.sector}"
                    )
                
                # Check daily loss limits
                daily_pnl = await self._calculate_daily_pnl(user.id, broker_account.id)
                daily_loss_percentage = abs(daily_pnl / portfolio_value * 100) if portfolio_value > 0 else 0
                
                if daily_loss_percentage > self.max_daily_loss * 100:
                    return RiskCheckResult(
                        is_valid=False,
                        risk_level=RiskLevel.CRITICAL,
                        reason=f"Daily loss limit exceeded: {daily_loss_percentage:.1f}%"
                    )
                
                # Check correlation risk
                correlation_risk = await self._calculate_correlation_risk(positions, stock)
                if correlation_risk > 0.8:
                    warnings.append(
                        f"High correlation risk with existing positions: {correlation_risk:.2f}"
                    )
                
                # Generate recommendations
                if position_percentage > max_position_limit * 0.8:
                    recommendations.append("Consider reducing position size")
                
                if len(positions) < 5:
                    recommendations.append("Consider diversifying across more positions")
                
                # Determine risk level
                risk_level = self._determine_risk_level(
                    position_percentage, sector_exposure, daily_loss_percentage, correlation_risk
                )
                
                return RiskCheckResult(
                    is_valid=True,
                    risk_level=risk_level,
                    warnings=warnings,
                    recommendations=recommendations
                )
                
        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return RiskCheckResult(
                is_valid=False,
                risk_level=RiskLevel.HIGH,
                reason=f"Risk validation error: {str(e)}"
            )
    
    async def calculate_position_risk(
        self,
        user_id: int,
        broker_account_id: int,
        symbol: str
    ) -> Optional[PositionRisk]:
        """Calculate risk metrics for a specific position"""
        try:
            async with get_db() as db:
                # Get position
                position_result = await db.execute(
                    select(Position).options(
                        selectinload(Position.stock)
                    ).where(
                        and_(
                            Position.user_id == user_id,
                            Position.broker_account_id == broker_account_id,
                            Position.stock.symbol == symbol.upper()
                        )
                    )
                )
                position = position_result.scalar_one_or_none()
                
                if not position:
                    return None
                
                # Get broker account for portfolio value
                account_result = await db.execute(
                    select(BrokerAccount).where(BrokerAccount.id == broker_account_id)
                )
                account = account_result.scalar_one_or_none()
                
                if not account:
                    return None
                
                # Calculate metrics
                position_value = abs(position.quantity * position.current_price) if position.current_price else 0
                portfolio_percentage = (position_value / account.total_equity * 100) if account.total_equity > 0 else 0
                
                # Simplified risk calculations (would use historical data in production)
                var_1_day = position_value * 0.02  # 2% daily VaR estimate
                beta = 1.0  # Would be calculated from historical data
                correlation_risk = 0.5  # Would be calculated from correlation matrix
                concentration_risk = portfolio_percentage / 100
                
                return PositionRisk(
                    symbol=symbol.upper(),
                    position_value=position_value,
                    portfolio_percentage=portfolio_percentage,
                    var_1_day=var_1_day,
                    beta=beta,
                    correlation_risk=correlation_risk,
                    concentration_risk=concentration_risk
                )
                
        except Exception as e:
            logger.error(f"Error calculating position risk for {symbol}: {e}")
            return None
    
    async def calculate_portfolio_risk(
        self,
        user_id: int,
        broker_account_id: int
    ) -> Optional[PortfolioRisk]:
        """Calculate comprehensive portfolio risk metrics"""
        try:
            async with get_db() as db:
                # Get broker account
                account_result = await db.execute(
                    select(BrokerAccount).where(
                        and_(
                            BrokerAccount.id == broker_account_id,
                            BrokerAccount.user_id == user_id
                        )
                    )
                )
                account = account_result.scalar_one_or_none()
                
                if not account:
                    return None
                
                # Get all positions
                positions_result = await db.execute(
                    select(Position).options(
                        selectinload(Position.stock)
                    ).where(
                        and_(
                            Position.user_id == user_id,
                            Position.broker_account_id == broker_account_id,
                            Position.quantity != 0
                        )
                    )
                )
                positions = positions_result.scalars().all()
                
                # Calculate basic metrics
                total_value = account.total_equity
                cash_percentage = (account.cash_balance / total_value * 100) if total_value > 0 else 100
                equity_percentage = 100 - cash_percentage
                
                # Calculate portfolio VaR (simplified)
                total_position_value = sum(
                    abs(pos.quantity * (pos.current_price or 0)) for pos in positions
                )
                var_1_day = total_position_value * 0.02  # 2% daily VaR
                var_5_day = var_1_day * (5 ** 0.5)  # Scale for 5 days
                expected_shortfall = var_1_day * 1.3  # Expected shortfall estimate
                
                # Calculate concentration risk
                concentration_risk = {}
                sector_exposure = {}
                
                for position in positions:
                    if position.stock and position.stock.sector:
                        sector = position.stock.sector
                        position_value = abs(position.quantity * (position.current_price or 0))
                        sector_exposure[sector] = sector_exposure.get(sector, 0) + position_value
                
                for sector, exposure in sector_exposure.items():
                    concentration_risk[sector] = (exposure / total_value * 100) if total_value > 0 else 0
                
                # Simplified metrics (would be calculated from historical data)
                portfolio_beta = 1.0
                sharpe_ratio = 1.2
                max_drawdown = 0.05
                
                return PortfolioRisk(
                    total_value=total_value,
                    cash_percentage=cash_percentage,
                    equity_percentage=equity_percentage,
                    var_1_day=var_1_day,
                    var_5_day=var_5_day,
                    expected_shortfall=expected_shortfall,
                    portfolio_beta=portfolio_beta,
                    sharpe_ratio=sharpe_ratio,
                    max_drawdown=max_drawdown,
                    concentration_risk=concentration_risk,
                    correlation_matrix={}  # Would be populated with correlation data
                )
                
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            return None

    async def _calculate_sector_exposure(
        self,
        positions: List[Position],
        new_stock: Stock,
        order_value: float,
        portfolio_value: float
    ) -> float:
        """Calculate sector exposure percentage including new order"""
        if not new_stock.sector or portfolio_value <= 0:
            return 0.0

        sector_exposure = order_value  # New order contribution

        for position in positions:
            if (position.stock and
                position.stock.sector == new_stock.sector and
                position.current_price):
                position_value = abs(position.quantity * position.current_price)
                sector_exposure += position_value

        return (sector_exposure / portfolio_value) * 100

    async def _calculate_daily_pnl(self, user_id: int, broker_account_id: int) -> float:
        """Calculate daily P&L for risk assessment"""
        try:
            async with get_db() as db:
                today = datetime.utcnow().date()

                # Get today's filled trades
                result = await db.execute(
                    select(Trade).where(
                        and_(
                            Trade.user_id == user_id,
                            Trade.broker_account_id == broker_account_id,
                            Trade.status == OrderStatus.FILLED,
                            func.date(Trade.filled_at) == today
                        )
                    )
                )
                trades = result.scalars().all()

                # Calculate realized P&L from trades
                realized_pnl = sum(trade.realized_pnl or 0 for trade in trades)

                # Get current positions for unrealized P&L
                positions_result = await db.execute(
                    select(Position).where(
                        and_(
                            Position.user_id == user_id,
                            Position.broker_account_id == broker_account_id,
                            Position.quantity != 0
                        )
                    )
                )
                positions = positions_result.scalars().all()

                # Calculate unrealized P&L (day change)
                unrealized_pnl = sum(position.day_pnl or 0 for position in positions)

                return realized_pnl + unrealized_pnl

        except Exception as e:
            logger.error(f"Error calculating daily P&L: {e}")
            return 0.0

    async def _calculate_correlation_risk(
        self,
        positions: List[Position],
        new_stock: Stock
    ) -> float:
        """Calculate correlation risk with existing positions"""
        # Simplified correlation calculation
        # In production, this would use historical price data

        if not positions or not new_stock.sector:
            return 0.0

        same_sector_positions = [
            pos for pos in positions
            if pos.stock and pos.stock.sector == new_stock.sector
        ]

        if not same_sector_positions:
            return 0.0

        # Return high correlation if many positions in same sector
        return min(len(same_sector_positions) * 0.2, 1.0)

    def _determine_risk_level(
        self,
        position_percentage: float,
        sector_exposure: float,
        daily_loss_percentage: float,
        correlation_risk: float
    ) -> RiskLevel:
        """Determine overall risk level based on various factors"""

        # Critical risk conditions
        if (daily_loss_percentage > self.max_daily_loss * 80 or
            position_percentage > self.max_position_size * 90 or
            sector_exposure > self.max_sector_concentration * 90):
            return RiskLevel.CRITICAL

        # High risk conditions
        if (daily_loss_percentage > self.max_daily_loss * 60 or
            position_percentage > self.max_position_size * 70 or
            sector_exposure > self.max_sector_concentration * 70 or
            correlation_risk > 0.8):
            return RiskLevel.HIGH

        # Medium risk conditions
        if (daily_loss_percentage > self.max_daily_loss * 40 or
            position_percentage > self.max_position_size * 50 or
            sector_exposure > self.max_sector_concentration * 50 or
            correlation_risk > 0.6):
            return RiskLevel.MEDIUM

        return RiskLevel.LOW

    async def check_stop_loss_triggers(
        self,
        user_id: int,
        broker_account_id: int
    ) -> List[Dict[str, Any]]:
        """Check for positions that should trigger stop losses"""
        triggered_stops = []

        try:
            async with get_db() as db:
                # Get positions with stop loss prices
                result = await db.execute(
                    select(Position).options(
                        selectinload(Position.stock)
                    ).where(
                        and_(
                            Position.user_id == user_id,
                            Position.broker_account_id == broker_account_id,
                            Position.quantity != 0,
                            Position.stop_loss_price.isnot(None)
                        )
                    )
                )
                positions = result.scalars().all()

                for position in positions:
                    if not position.current_price or not position.stop_loss_price:
                        continue

                    # Check if stop loss should trigger
                    should_trigger = False

                    if position.quantity > 0:  # Long position
                        should_trigger = position.current_price <= position.stop_loss_price
                    else:  # Short position
                        should_trigger = position.current_price >= position.stop_loss_price

                    if should_trigger:
                        triggered_stops.append({
                            "position_id": position.id,
                            "symbol": position.stock.symbol if position.stock else "Unknown",
                            "quantity": position.quantity,
                            "current_price": position.current_price,
                            "stop_loss_price": position.stop_loss_price,
                            "unrealized_pnl": position.unrealized_pnl or 0
                        })

        except Exception as e:
            logger.error(f"Error checking stop loss triggers: {e}")

        return triggered_stops

    async def get_risk_summary(
        self,
        user_id: int,
        broker_account_id: int
    ) -> Dict[str, Any]:
        """Get comprehensive risk summary for account"""
        try:
            portfolio_risk = await self.calculate_portfolio_risk(user_id, broker_account_id)
            daily_pnl = await self._calculate_daily_pnl(user_id, broker_account_id)
            stop_loss_triggers = await self.check_stop_loss_triggers(user_id, broker_account_id)

            if not portfolio_risk:
                return {"error": "Unable to calculate portfolio risk"}

            # Determine overall risk status
            risk_factors = []

            if portfolio_risk.cash_percentage < 10:
                risk_factors.append("Low cash reserves")

            if portfolio_risk.var_1_day > portfolio_risk.total_value * 0.05:
                risk_factors.append("High daily VaR")

            max_concentration = max(portfolio_risk.concentration_risk.values()) if portfolio_risk.concentration_risk else 0
            if max_concentration > 30:
                risk_factors.append("High sector concentration")

            if len(stop_loss_triggers) > 0:
                risk_factors.append(f"{len(stop_loss_triggers)} stop loss triggers")

            overall_risk = RiskLevel.LOW
            if len(risk_factors) >= 3:
                overall_risk = RiskLevel.HIGH
            elif len(risk_factors) >= 2:
                overall_risk = RiskLevel.MEDIUM
            elif len(risk_factors) >= 1:
                overall_risk = RiskLevel.MEDIUM

            return {
                "overall_risk": overall_risk.value,
                "portfolio_value": portfolio_risk.total_value,
                "daily_pnl": daily_pnl,
                "var_1_day": portfolio_risk.var_1_day,
                "cash_percentage": portfolio_risk.cash_percentage,
                "max_drawdown": portfolio_risk.max_drawdown,
                "concentration_risk": portfolio_risk.concentration_risk,
                "risk_factors": risk_factors,
                "stop_loss_triggers": len(stop_loss_triggers),
                "recommendations": self._generate_risk_recommendations(portfolio_risk, risk_factors)
            }

        except Exception as e:
            logger.error(f"Error generating risk summary: {e}")
            return {"error": str(e)}

    def _generate_risk_recommendations(
        self,
        portfolio_risk: PortfolioRisk,
        risk_factors: List[str]
    ) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []

        if portfolio_risk.cash_percentage < 10:
            recommendations.append("Consider increasing cash reserves to at least 10%")

        max_concentration = max(portfolio_risk.concentration_risk.values()) if portfolio_risk.concentration_risk else 0
        if max_concentration > 25:
            recommendations.append("Reduce sector concentration by diversifying holdings")

        if portfolio_risk.var_1_day > portfolio_risk.total_value * 0.03:
            recommendations.append("Consider reducing position sizes to lower daily VaR")

        if portfolio_risk.sharpe_ratio < 1.0:
            recommendations.append("Review strategy performance and risk-adjusted returns")

        if len(risk_factors) > 2:
            recommendations.append("Consider implementing stricter risk controls")

        return recommendations
