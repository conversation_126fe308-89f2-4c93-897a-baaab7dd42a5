"""
Trading API endpoints
Provides comprehensive trading functionality with risk management and order execution
"""

from fastapi import APIRouter, HTTPException, Depends, status, Query, BackgroundTasks
from fastapi.security import HTTPBearer
from pydantic import BaseModel, Field, validator
from sqlalchemy import select, and_, or_, desc
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from enum import Enum
import asyncio

from app.core.database import get_db, CacheManager
from app.core.security import get_current_active_user, require_subscription_tier
from app.models.user import User
from app.models.trade import Trade, Position, OrderType, OrderSide, OrderStatus
from app.models.user import BrokerAccount
from app.models.stock import Stock
from app.services.trading_engine import TradingEngine
from app.services.risk_manager import RiskManager
from app.services.broker_integration import BrokerManager
from app.utils.rate_limiter import RateLimiter
from app.core.config import settings

router = APIRouter()
security = HTTPBearer()

# Rate limiter for trading operations
trading_rate_limiter = RateLimiter(
    max_requests=50,  # Free tier: 50 trading requests per hour
    time_window=3600,
    premium_max_requests=500,  # Premium tier: 500 requests per hour
    enterprise_max_requests=5000  # Enterprise tier: 5000 requests per hour
)

# Enums for trading parameters
class TimeInForce(str, Enum):
    DAY = "day"
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate or Cancel
    FOK = "fok"  # Fill or Kill

class PositionSide(str, Enum):
    LONG = "long"
    SHORT = "short"

# Pydantic models for request/response
class OrderRequest(BaseModel):
    """Order submission request"""
    symbol: str = Field(..., min_length=1, max_length=10)
    order_type: OrderType
    order_side: OrderSide
    quantity: int = Field(..., gt=0, le=10000)
    price: Optional[float] = Field(None, gt=0)
    stop_price: Optional[float] = Field(None, gt=0)
    time_in_force: TimeInForce = Field(default=TimeInForce.DAY)
    broker_account_id: int
    stop_loss_price: Optional[float] = Field(None, gt=0)
    take_profit_price: Optional[float] = Field(None, gt=0)
    is_paper_trade: bool = Field(default=True)

    @validator('price')
    def validate_price_for_limit_orders(cls, v, values):
        if values.get('order_type') in [OrderType.LIMIT, OrderType.STOP_LIMIT] and v is None:
            raise ValueError('Price is required for limit orders')
        return v

    @validator('stop_price')
    def validate_stop_price(cls, v, values):
        if values.get('order_type') in [OrderType.STOP, OrderType.STOP_LIMIT] and v is None:
            raise ValueError('Stop price is required for stop orders')
        return v

class OrderResponse(BaseModel):
    """Order response model"""
    id: int
    symbol: str
    order_type: str
    order_side: str
    quantity: int
    price: Optional[float]
    stop_price: Optional[float]
    filled_quantity: int
    average_fill_price: Optional[float]
    status: str
    time_in_force: str
    broker_order_id: Optional[str]
    broker_account_id: int
    stop_loss_price: Optional[float]
    take_profit_price: Optional[float]
    is_paper_trade: bool
    created_at: str
    updated_at: Optional[str]
    filled_at: Optional[str]

class PositionResponse(BaseModel):
    """Position response model"""
    id: int
    symbol: str
    quantity: int
    average_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    realized_pnl: float
    side: str
    broker_account_id: int
    is_paper_trade: bool
    opened_at: str
    updated_at: Optional[str]

class TradeResponse(BaseModel):
    """Trade response model"""
    id: int
    symbol: str
    order_type: str
    order_side: str
    quantity: int
    price: float
    total_value: float
    commission: float
    net_value: float
    broker_order_id: Optional[str]
    broker_account_id: int
    strategy_id: Optional[int]
    is_paper_trade: bool
    executed_at: str

class BrokerAccountResponse(BaseModel):
    """Broker account response model"""
    id: int
    broker_name: str
    account_number: str
    account_type: str
    is_active: bool
    is_paper_trading: bool
    buying_power: float
    cash_balance: float
    market_value: float
    total_equity: float
    day_trading_buying_power: Optional[float]
    maintenance_margin: Optional[float]
    created_at: str
    updated_at: Optional[str]

class RiskMetrics(BaseModel):
    """Risk metrics response"""
    portfolio_value: float
    cash_balance: float
    buying_power: float
    day_trading_buying_power: float
    maintenance_margin: float
    portfolio_beta: float
    var_1_day: float  # Value at Risk
    max_drawdown: float
    sharpe_ratio: float
    positions_count: int
    concentration_risk: Dict[str, float]  # Sector/stock concentration
    margin_utilization: float

@router.get("/accounts", response_model=List[BrokerAccountResponse])
async def get_broker_accounts(
    current_user: User = Depends(get_current_active_user),
    active_only: bool = Query(True)
):
    """Get user's broker accounts"""
    async with get_db() as db:
        query = select(BrokerAccount).where(BrokerAccount.user_id == current_user.id)
        
        if active_only:
            query = query.where(BrokerAccount.is_active == True)
        
        query = query.order_by(desc(BrokerAccount.created_at))
        
        result = await db.execute(query)
        accounts = result.scalars().all()
        
        return [
            BrokerAccountResponse(
                id=account.id,
                broker_name=account.broker_name,
                account_number=account.account_number,
                account_type=account.account_type,
                is_active=account.is_active,
                is_paper_trading=account.is_paper_trading,
                buying_power=account.buying_power,
                cash_balance=account.cash_balance,
                market_value=account.market_value,
                total_equity=account.total_equity,
                day_trading_buying_power=account.day_trading_buying_power,
                maintenance_margin=account.maintenance_margin,
                created_at=account.created_at.isoformat(),
                updated_at=account.updated_at.isoformat() if account.updated_at else None
            )
            for account in accounts
        ]

@router.post("/orders", response_model=OrderResponse, status_code=status.HTTP_201_CREATED)
async def submit_order(
    order_request: OrderRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Submit a new trading order"""
    # Apply rate limiting
    await trading_rate_limiter.check_rate_limit(
        user_id=current_user.id,
        subscription_tier=current_user.subscription_tier
    )
    
    async with get_db() as db:
        # Verify broker account ownership
        result = await db.execute(
            select(BrokerAccount).where(
                and_(
                    BrokerAccount.id == order_request.broker_account_id,
                    BrokerAccount.user_id == current_user.id,
                    BrokerAccount.is_active == True
                )
            )
        )
        broker_account = result.scalar_one_or_none()
        
        if not broker_account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Broker account not found or inactive"
            )
        
        # Get or create stock record
        result = await db.execute(
            select(Stock).where(Stock.symbol == order_request.symbol.upper())
        )
        stock = result.scalar_one_or_none()
        
        if not stock:
            stock = Stock(
                symbol=order_request.symbol.upper(),
                name=order_request.symbol.upper()
            )
            db.add(stock)
            await db.commit()
            await db.refresh(stock)
        
        # Risk management checks
        risk_manager = RiskManager()
        risk_check = await risk_manager.validate_order(
            user=current_user,
            broker_account=broker_account,
            order_request=order_request
        )
        
        if not risk_check.is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Risk check failed: {risk_check.reason}"
            )
        
        # Create trade record
        new_trade = Trade(
            user_id=current_user.id,
            stock_id=stock.id,
            broker_account_id=broker_account.id,
            order_type=order_request.order_type,
            order_side=order_request.order_side,
            quantity=order_request.quantity,
            price=order_request.price,
            stop_price=order_request.stop_price,
            time_in_force=order_request.time_in_force.value,
            stop_loss_price=order_request.stop_loss_price,
            take_profit_price=order_request.take_profit_price,
            is_paper_trade=order_request.is_paper_trade,
            status=OrderStatus.PENDING
        )
        
        db.add(new_trade)
        await db.commit()
        await db.refresh(new_trade)
        
        # Submit order to broker (background task)
        background_tasks.add_task(
            _submit_order_to_broker,
            new_trade.id,
            broker_account.id
        )
        
        return OrderResponse(
            id=new_trade.id,
            symbol=stock.symbol,
            order_type=new_trade.order_type.value,
            order_side=new_trade.order_side.value,
            quantity=new_trade.quantity,
            price=new_trade.price,
            stop_price=new_trade.stop_price,
            filled_quantity=new_trade.filled_quantity,
            average_fill_price=new_trade.average_fill_price,
            status=new_trade.status.value,
            time_in_force=new_trade.time_in_force,
            broker_order_id=new_trade.broker_order_id,
            broker_account_id=new_trade.broker_account_id,
            stop_loss_price=new_trade.stop_loss_price,
            take_profit_price=new_trade.take_profit_price,
            is_paper_trade=new_trade.is_paper_trade,
            created_at=new_trade.created_at.isoformat(),
            updated_at=new_trade.updated_at.isoformat() if new_trade.updated_at else None,
            filled_at=new_trade.filled_at.isoformat() if new_trade.filled_at else None
        )

async def _submit_order_to_broker(trade_id: int, broker_account_id: int):
    """Background task to submit order to broker"""
    try:
        async with get_db() as db:
            # Get trade and broker account
            result = await db.execute(
                select(Trade).options(
                    selectinload(Trade.stock),
                    selectinload(Trade.broker_account)
                ).where(Trade.id == trade_id)
            )
            trade = result.scalar_one_or_none()
            
            if not trade:
                return
            
            # Get trading engine
            from app.main import trading_engine
            
            if not trading_engine:
                trade.status = OrderStatus.REJECTED
                trade.rejection_reason = "Trading engine not available"
                await db.commit()
                return
            
            # Submit order
            await trading_engine.submit_order(trade)
            
    except Exception as e:
        # Update trade status on error
        async with get_db() as db:
            result = await db.execute(
                select(Trade).where(Trade.id == trade_id)
            )
            trade = result.scalar_one_or_none()
            
            if trade:
                trade.status = OrderStatus.REJECTED
                trade.rejection_reason = str(e)
                await db.commit()

@router.get("/orders", response_model=List[OrderResponse])
async def get_orders(
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    status: Optional[OrderStatus] = Query(None),
    symbol: Optional[str] = Query(None),
    broker_account_id: Optional[int] = Query(None)
):
    """Get user's trading orders"""
    async with get_db() as db:
        query = select(Trade).options(
            selectinload(Trade.stock),
            selectinload(Trade.broker_account)
        ).where(Trade.user_id == current_user.id)

        if status:
            query = query.where(Trade.status == status)

        if symbol:
            query = query.join(Stock).where(Stock.symbol == symbol.upper())

        if broker_account_id:
            query = query.where(Trade.broker_account_id == broker_account_id)

        query = query.order_by(desc(Trade.created_at)).offset(skip).limit(limit)

        result = await db.execute(query)
        trades = result.scalars().all()

        return [
            OrderResponse(
                id=trade.id,
                symbol=trade.stock.symbol,
                order_type=trade.order_type.value,
                order_side=trade.order_side.value,
                quantity=trade.quantity,
                price=trade.price,
                stop_price=trade.stop_price,
                filled_quantity=trade.filled_quantity,
                average_fill_price=trade.average_fill_price,
                status=trade.status.value,
                time_in_force=trade.time_in_force,
                broker_order_id=trade.broker_order_id,
                broker_account_id=trade.broker_account_id,
                stop_loss_price=trade.stop_loss_price,
                take_profit_price=trade.take_profit_price,
                is_paper_trade=trade.is_paper_trade,
                created_at=trade.created_at.isoformat(),
                updated_at=trade.updated_at.isoformat() if trade.updated_at else None,
                filled_at=trade.filled_at.isoformat() if trade.filled_at else None
            )
            for trade in trades
        ]

@router.get("/orders/{order_id}", response_model=OrderResponse)
async def get_order(
    order_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Get specific order details"""
    async with get_db() as db:
        result = await db.execute(
            select(Trade).options(
                selectinload(Trade.stock),
                selectinload(Trade.broker_account)
            ).where(
                and_(
                    Trade.id == order_id,
                    Trade.user_id == current_user.id
                )
            )
        )
        trade = result.scalar_one_or_none()

        if not trade:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )

        return OrderResponse(
            id=trade.id,
            symbol=trade.stock.symbol,
            order_type=trade.order_type.value,
            order_side=trade.order_side.value,
            quantity=trade.quantity,
            price=trade.price,
            stop_price=trade.stop_price,
            filled_quantity=trade.filled_quantity,
            average_fill_price=trade.average_fill_price,
            status=trade.status.value,
            time_in_force=trade.time_in_force,
            broker_order_id=trade.broker_order_id,
            broker_account_id=trade.broker_account_id,
            stop_loss_price=trade.stop_loss_price,
            take_profit_price=trade.take_profit_price,
            is_paper_trade=trade.is_paper_trade,
            created_at=trade.created_at.isoformat(),
            updated_at=trade.updated_at.isoformat() if trade.updated_at else None,
            filled_at=trade.filled_at.isoformat() if trade.filled_at else None
        )

@router.delete("/orders/{order_id}", status_code=status.HTTP_204_NO_CONTENT)
async def cancel_order(
    order_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Cancel a pending order"""
    async with get_db() as db:
        result = await db.execute(
            select(Trade).where(
                and_(
                    Trade.id == order_id,
                    Trade.user_id == current_user.id
                )
            )
        )
        trade = result.scalar_one_or_none()

        if not trade:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )

        if trade.status not in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Order cannot be cancelled in current status"
            )

        # Cancel order with broker
        try:
            from app.main import trading_engine

            if trading_engine:
                await trading_engine.cancel_order(trade)
            else:
                # Fallback: mark as cancelled locally
                trade.status = OrderStatus.CANCELLED
                trade.cancelled_at = datetime.utcnow()
                await db.commit()

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error cancelling order: {str(e)}"
            )

@router.get("/positions", response_model=List[PositionResponse])
async def get_positions(
    current_user: User = Depends(get_current_active_user),
    broker_account_id: Optional[int] = Query(None),
    symbol: Optional[str] = Query(None),
    include_closed: bool = Query(False)
):
    """Get user's current positions"""
    async with get_db() as db:
        query = select(Position).options(
            selectinload(Position.stock),
            selectinload(Position.broker_account)
        ).where(Position.user_id == current_user.id)

        if broker_account_id:
            query = query.where(Position.broker_account_id == broker_account_id)

        if symbol:
            query = query.join(Stock).where(Stock.symbol == symbol.upper())

        if not include_closed:
            query = query.where(Position.quantity != 0)

        query = query.order_by(desc(Position.updated_at))

        result = await db.execute(query)
        positions = result.scalars().all()

        # Get current market prices for P&L calculation
        from app.main import data_provider

        position_responses = []
        for position in positions:
            current_price = position.average_cost  # Fallback

            if data_provider:
                try:
                    quote_data = await data_provider.get_stock_quote(position.stock.symbol)
                    if quote_data and 'price' in quote_data:
                        current_price = quote_data['price']
                except:
                    pass

            market_value = position.quantity * current_price
            unrealized_pnl = market_value - (position.quantity * position.average_cost)
            unrealized_pnl_percent = (unrealized_pnl / (position.quantity * position.average_cost)) * 100 if position.quantity * position.average_cost != 0 else 0

            position_responses.append(
                PositionResponse(
                    id=position.id,
                    symbol=position.stock.symbol,
                    quantity=position.quantity,
                    average_cost=position.average_cost,
                    current_price=current_price,
                    market_value=market_value,
                    unrealized_pnl=unrealized_pnl,
                    unrealized_pnl_percent=unrealized_pnl_percent,
                    realized_pnl=position.realized_pnl,
                    side=PositionSide.LONG.value if position.quantity > 0 else PositionSide.SHORT.value,
                    broker_account_id=position.broker_account_id,
                    is_paper_trade=position.broker_account.is_paper_trading,
                    opened_at=position.opened_at.isoformat(),
                    updated_at=position.updated_at.isoformat() if position.updated_at else None
                )
            )

        return position_responses

@router.get("/trades", response_model=List[TradeResponse])
async def get_trades(
    current_user: User = Depends(get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    symbol: Optional[str] = Query(None),
    broker_account_id: Optional[int] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None)
):
    """Get user's executed trades"""
    async with get_db() as db:
        query = select(Trade).options(
            selectinload(Trade.stock),
            selectinload(Trade.broker_account)
        ).where(
            and_(
                Trade.user_id == current_user.id,
                Trade.status == OrderStatus.FILLED
            )
        )

        if symbol:
            query = query.join(Stock).where(Stock.symbol == symbol.upper())

        if broker_account_id:
            query = query.where(Trade.broker_account_id == broker_account_id)

        if start_date:
            query = query.where(Trade.filled_at >= start_date)

        if end_date:
            query = query.where(Trade.filled_at <= end_date)

        query = query.order_by(desc(Trade.filled_at)).offset(skip).limit(limit)

        result = await db.execute(query)
        trades = result.scalars().all()

        return [
            TradeResponse(
                id=trade.id,
                symbol=trade.stock.symbol,
                order_type=trade.order_type.value,
                order_side=trade.order_side.value,
                quantity=trade.filled_quantity,
                price=trade.average_fill_price,
                total_value=trade.filled_quantity * trade.average_fill_price,
                commission=trade.commission or 0.0,
                net_value=(trade.filled_quantity * trade.average_fill_price) - (trade.commission or 0.0),
                broker_order_id=trade.broker_order_id,
                broker_account_id=trade.broker_account_id,
                strategy_id=trade.strategy_id,
                is_paper_trade=trade.is_paper_trade,
                executed_at=trade.filled_at.isoformat()
            )
            for trade in trades
        ]

@router.get("/risk-metrics", response_model=RiskMetrics)
async def get_risk_metrics(
    current_user: User = Depends(get_current_active_user),
    broker_account_id: Optional[int] = Query(None)
):
    """Get portfolio risk metrics"""
    async with get_db() as db:
        # Get broker accounts
        account_query = select(BrokerAccount).where(
            and_(
                BrokerAccount.user_id == current_user.id,
                BrokerAccount.is_active == True
            )
        )

        if broker_account_id:
            account_query = account_query.where(BrokerAccount.id == broker_account_id)

        result = await db.execute(account_query)
        accounts = result.scalars().all()

        if not accounts:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active broker accounts found"
            )

        # Calculate aggregate metrics
        total_portfolio_value = sum(account.total_equity for account in accounts)
        total_cash_balance = sum(account.cash_balance for account in accounts)
        total_buying_power = sum(account.buying_power for account in accounts)
        total_day_trading_power = sum(account.day_trading_buying_power or 0 for account in accounts)
        total_maintenance_margin = sum(account.maintenance_margin or 0 for account in accounts)

        # Get positions for risk calculations
        positions_query = select(Position).options(
            selectinload(Position.stock)
        ).where(
            and_(
                Position.user_id == current_user.id,
                Position.quantity != 0
            )
        )

        if broker_account_id:
            positions_query = positions_query.where(Position.broker_account_id == broker_account_id)

        result = await db.execute(positions_query)
        positions = result.scalars().all()

        # Calculate concentration risk
        concentration_risk = {}
        total_position_value = 0

        for position in positions:
            position_value = abs(position.quantity * position.average_cost)
            total_position_value += position_value

            # Stock concentration
            stock_key = f"stock_{position.stock.symbol}"
            concentration_risk[stock_key] = concentration_risk.get(stock_key, 0) + position_value

        # Convert to percentages
        if total_position_value > 0:
            for key in concentration_risk:
                concentration_risk[key] = (concentration_risk[key] / total_position_value) * 100

        # Calculate portfolio beta (simplified)
        portfolio_beta = 1.0  # Would be calculated from individual stock betas

        # Calculate VaR (simplified - would use historical data)
        var_1_day = total_portfolio_value * 0.02  # 2% daily VaR estimate

        # Calculate max drawdown (simplified)
        max_drawdown = 0.05  # 5% estimate - would be calculated from historical performance

        # Calculate Sharpe ratio (simplified)
        sharpe_ratio = 1.2  # Would be calculated from returns and risk-free rate

        # Margin utilization
        margin_utilization = 0.0
        if total_buying_power > 0:
            margin_utilization = ((total_portfolio_value - total_cash_balance) / total_buying_power) * 100

        return RiskMetrics(
            portfolio_value=total_portfolio_value,
            cash_balance=total_cash_balance,
            buying_power=total_buying_power,
            day_trading_buying_power=total_day_trading_power,
            maintenance_margin=total_maintenance_margin,
            portfolio_beta=portfolio_beta,
            var_1_day=var_1_day,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            positions_count=len(positions),
            concentration_risk=concentration_risk,
            margin_utilization=margin_utilization
        )

@router.post("/accounts/{account_id}/sync")
async def sync_broker_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user)
):
    """Sync broker account data"""
    async with get_db() as db:
        result = await db.execute(
            select(BrokerAccount).where(
                and_(
                    BrokerAccount.id == account_id,
                    BrokerAccount.user_id == current_user.id
                )
            )
        )
        account = result.scalar_one_or_none()

        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Broker account not found"
            )

        try:
            # Sync account data with broker
            broker_manager = BrokerManager()
            await broker_manager.sync_account_data(account)

            return {"message": "Account sync completed successfully"}

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error syncing account: {str(e)}"
            )

@router.get("/market-hours")
async def get_market_hours(
    current_user: User = Depends(get_current_active_user)
):
    """Get current market hours and status"""
    try:
        from app.main import data_provider

        if not data_provider:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Data provider not available"
            )

        market_status = await data_provider.get_market_status()

        return {
            "is_open": market_status.get("is_open", False),
            "next_open": market_status.get("next_open"),
            "next_close": market_status.get("next_close"),
            "timezone": market_status.get("timezone", "US/Eastern"),
            "current_time": datetime.utcnow().isoformat(),
            "trading_calendar": market_status.get("trading_calendar", {})
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error fetching market hours: {str(e)}"
        )
